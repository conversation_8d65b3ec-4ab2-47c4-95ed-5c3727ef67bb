/* Modern Video Player CSS */
:root {
  --mvp-primary: #007bff;
  --mvp-primary-hover: #0056b3;
  --mvp-bg-overlay: rgba(0, 0, 0, 0.7);
  --mvp-bg-controls: rgba(0, 0, 0, 0.8);
  --mvp-text-primary: #ffffff;
  --mvp-text-secondary: #cccccc;
  --mvp-border-radius: 8px;
  --mvp-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --mvp-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* Base container */
.modern-video-player,
.mvp-container {
  position: relative;
  width: 100%;
  max-width: 100%;
  background: #000;
  border-radius: var(--mvp-border-radius);
  overflow: hidden;
  box-shadow: var(--mvp-shadow);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
  user-select: none;
  aspect-ratio: 16/9;
}

/* Video wrapper */
.mvp-video-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  background: #000;
  cursor: pointer;
}

/* Video element */
.mvp-video {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background: #000;
  cursor: pointer;
}

/* YouTube wrapper */
.mvp-youtube-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #000;
  display: none; /* Hidden by default, shown when YouTube is initialized */
}

.mvp-youtube-player {
  width: 100%;
  height: 100%;
}

.mvp-youtube-player iframe {
  width: 100%;
  height: 100%;
}

/* Loading spinner */
.mvp-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}

.mvp-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid var(--mvp-primary);
  border-radius: 50%;
  animation: mvp-spin 1s linear infinite;
}

@keyframes mvp-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Play overlay */
.mvp-play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--mvp-bg-overlay);
  opacity: 1;
  transition: var(--mvp-transition);
  z-index: 5;
}

.mvp-play-overlay.hidden {
  opacity: 0;
  pointer-events: none;
}

.mvp-play-btn-large {
  width: 80px;
  height: 80px;
  border: none;
  background: var(--mvp-primary);
  border-radius: 50%;
  color: white;
  cursor: pointer;
  transition: var(--mvp-transition);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 20px rgba(0, 123, 255, 0.4);
}

.mvp-play-btn-large:hover {
  background: var(--mvp-primary-hover);
  transform: scale(1.1);
}

.mvp-play-btn-large svg {
  width: 32px;
  height: 32px;
  margin-left: 4px;
}

/* Controls overlay */
.mvp-controls-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  opacity: 0;
  transition: var(--mvp-transition);
  z-index: 15;
  pointer-events: none;
}



.mvp-container:hover .mvp-controls-overlay,
.mvp-controls-overlay.visible {
  opacity: 1;
}

.mvp-container:hover .mvp-controls-overlay *,
.mvp-controls-overlay.visible * {
  pointer-events: auto;
}

/* Top controls */
.mvp-top-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: linear-gradient(180deg, var(--mvp-bg-controls) 0%, transparent 100%);
}

.mvp-title {
  color: var(--mvp-text-primary);
  font-size: 16px;
  font-weight: 500;
}

/* Bottom controls */
.mvp-bottom-controls {
  padding: 0 20px 20px;
  background: linear-gradient(0deg, var(--mvp-bg-controls) 0%, transparent 100%);
}

/* Progress container */
.mvp-progress-container {
  position: relative;
  margin-bottom: 15px;
  height: 20px;
  display: flex;
  align-items: center;
}

.mvp-progress-bar {
  position: relative;
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  cursor: pointer;
  transition: height 0.2s ease;
}

.mvp-progress-container:hover .mvp-progress-bar {
  height: 6px;
}

.mvp-progress-buffer {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 2px;
  width: 0%;
  transition: width 0.3s ease;
}

.mvp-progress-played {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: var(--mvp-primary);
  border-radius: 2px;
  width: 0%;
  transition: width 0.1s ease;
}

.mvp-progress-handle {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 12px;
  height: 12px;
  background: var(--mvp-primary);
  border-radius: 50%;
  opacity: 0;
  transition: var(--mvp-transition);
  left: 0%;
}

.mvp-progress-container:hover .mvp-progress-handle {
  opacity: 1;
}

/* Time tooltip */
.mvp-time-tooltip {
  position: absolute;
  bottom: 25px;
  transform: translateX(-50%);
  background: var(--mvp-bg-controls);
  color: var(--mvp-text-primary);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  opacity: 0;
  transition: var(--mvp-transition);
  pointer-events: none;
  white-space: nowrap;
}

.mvp-time-tooltip.visible {
  opacity: 1;
}

/* Controls row */
.mvp-controls-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mvp-controls-left,
.mvp-controls-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Buttons */
.mvp-controls-overlay button {
  background: none;
  border: none;
  color: var(--mvp-text-primary);
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: var(--mvp-transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.mvp-controls-overlay button:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--mvp-primary);
}

.mvp-controls-overlay button svg {
  width: 20px;
  height: 20px;
}

.mvp-play-btn svg {
  width: 24px;
  height: 24px;
}

/* Volume container */
.mvp-volume-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mvp-volume-slider {
  width: 0;
  overflow: hidden;
  transition: width 0.3s ease;
}

.mvp-volume-container:hover .mvp-volume-slider {
  width: 80px;
}

.mvp-volume-bar {
  position: relative;
  width: 80px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  cursor: pointer;
}

.mvp-volume-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: var(--mvp-primary);
  border-radius: 2px;
  width: 100%;
  transition: width 0.1s ease;
}

.mvp-volume-handle {
  position: absolute;
  top: 50%;
  right: 0;
  transform: translate(50%, -50%);
  width: 12px;
  height: 12px;
  background: var(--mvp-primary);
  border-radius: 50%;
  cursor: pointer;
}

/* Time display */
.mvp-time-display {
  color: var(--mvp-text-secondary);
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
}

.mvp-time-separator {
  margin: 0 4px;
}

/* Settings menu */
.mvp-settings-menu {
  position: absolute;
  bottom: 70px;
  right: 20px;
  background: var(--mvp-bg-controls);
  border-radius: var(--mvp-border-radius);
  padding: 15px;
  min-width: 200px;
  z-index: 20;
  backdrop-filter: blur(10px);
}

.mvp-settings-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  color: var(--mvp-text-primary);
  font-size: 14px;
}

.mvp-settings-item:last-child {
  margin-bottom: 0;
}

.mvp-settings-item select {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: var(--mvp-text-primary);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

/* Responsive design */
@media (max-width: 768px) {
  .mvp-top-controls,
  .mvp-bottom-controls {
    padding: 15px;
  }
  
  .mvp-controls-left,
  .mvp-controls-right {
    gap: 8px;
  }
  
  .mvp-controls-overlay button {
    padding: 6px;
  }
  
  .mvp-controls-overlay button svg {
    width: 18px;
    height: 18px;
  }
  
  .mvp-play-btn svg {
    width: 20px;
    height: 20px;
  }
  
  .mvp-play-btn-large {
    width: 60px;
    height: 60px;
  }
  
  .mvp-play-btn-large svg {
    width: 24px;
    height: 24px;
  }
  
  .mvp-time-display {
    font-size: 12px;
  }
  
  .mvp-volume-container:hover .mvp-volume-slider {
    width: 60px;
  }
  
  .mvp-volume-bar {
    width: 60px;
  }
  
  .mvp-settings-menu {
    right: 10px;
    bottom: 60px;
    min-width: 180px;
  }
}

@media (max-width: 480px) {
  .mvp-top-controls,
  .mvp-bottom-controls {
    padding: 10px;
  }
  
  .mvp-progress-container {
    margin-bottom: 10px;
  }
  
  .mvp-controls-left,
  .mvp-controls-right {
    gap: 6px;
  }
  
  .mvp-volume-slider {
    display: none;
  }
  
  .mvp-settings-menu {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    border-radius: var(--mvp-border-radius) var(--mvp-border-radius) 0 0;
    min-width: auto;
  }
}

/* Fullscreen styles */
.mvp-container:-webkit-full-screen {
  width: 100vw;
  height: 100vh;
  border-radius: 0;
}

.mvp-container:-moz-full-screen {
  width: 100vw;
  height: 100vh;
  border-radius: 0;
}

.mvp-container:fullscreen {
  width: 100vw;
  height: 100vh;
  border-radius: 0;
}

/* Lazy loading placeholder */
.modern-video-player[data-lazy="true"]:not([data-initialized]) {
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.modern-video-player[data-lazy="true"]:not([data-initialized])::before {
  content: "📹 Video sẽ tải khi cuộn đến đây...";
  color: #666;
  font-size: 16px;
  text-align: center;
  padding: 20px;
}

/* GPU acceleration */
.mvp-video,
.mvp-controls-overlay,
.mvp-progress-played,
.mvp-volume-fill {
  will-change: transform;
  transform: translateZ(0);
}

/* Smooth animations */
* {
  box-sizing: border-box;
}

.mvp-container * {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
