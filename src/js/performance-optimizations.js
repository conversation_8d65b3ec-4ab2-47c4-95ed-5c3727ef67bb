/**
 * Performance Optimizations for Modern Video Player
 * Includes lazy loading, GPU acceleration, prefetch, and other optimizations
 */

class VideoPerformanceOptimizer {
    constructor() {
        this.prefetchedVideos = new Set();
        this.intersectionObserver = null;
        this.init();
    }
    
    init() {
        this.setupLazyLoading();
        this.setupPrefetching();
        this.enableGPUAcceleration();
        this.optimizeVideoElements();
    }
    
    // Lazy Loading Implementation
    setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            this.intersectionObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.loadVideo(entry.target);
                        this.intersectionObserver.unobserve(entry.target);
                    }
                });
            }, {
                rootMargin: '100px', // Start loading 100px before entering viewport
                threshold: 0.1
            });
            
            // Observe all video containers
            this.observeVideoContainers();
        }
    }
    
    observeVideoContainers() {
        const containers = document.querySelectorAll('.modern-video-player[data-lazy="true"]');
        containers.forEach(container => {
            this.intersectionObserver.observe(container);
        });
    }
    
    loadVideo(container) {
        const src = container.dataset.src;
        const youtubeId = container.dataset.youtubeId;
        
        if (src || youtubeId) {
            const options = {
                src: src,
                youtubeId: youtubeId,
                autoplay: container.dataset.autoplay === 'true',
                muted: container.dataset.muted === 'true',
                poster: container.dataset.poster
            };
            
            // Remove lazy loading indicator
            container.removeAttribute('data-lazy');
            
            // Initialize video player
            new ModernVideoPlayer(container, options);
        }
    }
    
    // Video Prefetching
    setupPrefetching() {
        // Prefetch videos that are likely to be played next
        this.prefetchNearbyVideos();
        
        // Prefetch on hover
        document.addEventListener('mouseover', (e) => {
            const container = e.target.closest('.modern-video-player');
            if (container && !this.prefetchedVideos.has(container)) {
                this.prefetchVideo(container);
            }
        });
    }
    
    prefetchNearbyVideos() {
        const containers = document.querySelectorAll('.modern-video-player');
        const viewportHeight = window.innerHeight;
        
        containers.forEach(container => {
            const rect = container.getBoundingClientRect();
            const distanceFromViewport = Math.abs(rect.top - viewportHeight);
            
            // Prefetch videos within 2 viewport heights
            if (distanceFromViewport < viewportHeight * 2) {
                this.prefetchVideo(container);
            }
        });
    }
    
    prefetchVideo(container) {
        if (this.prefetchedVideos.has(container)) return;
        
        const src = container.dataset.src;
        if (src && !src.includes('youtube.com')) {
            // Create invisible video element for prefetching
            const prefetchVideo = document.createElement('video');
            prefetchVideo.preload = 'metadata';
            prefetchVideo.src = src;
            prefetchVideo.style.display = 'none';
            
            // Add to DOM temporarily
            document.body.appendChild(prefetchVideo);
            
            // Remove after metadata is loaded
            prefetchVideo.addEventListener('loadedmetadata', () => {
                document.body.removeChild(prefetchVideo);
            });
            
            this.prefetchedVideos.add(container);
        }
    }
    
    // GPU Acceleration
    enableGPUAcceleration() {
        const style = document.createElement('style');
        style.textContent = `
            .mvp-video,
            .mvp-controls-overlay,
            .mvp-progress-played,
            .mvp-volume-fill,
            .mvp-progress-handle,
            .mvp-volume-handle {
                will-change: transform, opacity;
                transform: translateZ(0);
                backface-visibility: hidden;
                perspective: 1000px;
            }
            
            .mvp-container {
                contain: layout style paint;
            }
            
            .mvp-progress-bar,
            .mvp-volume-bar {
                contain: layout paint;
            }
        `;
        document.head.appendChild(style);
    }
    
    // Video Element Optimizations
    optimizeVideoElements() {
        // Optimize existing video elements
        document.querySelectorAll('video').forEach(video => {
            this.optimizeVideoElement(video);
        });
        
        // Optimize new video elements
        const observer = new MutationObserver((mutations) => {
            mutations.forEach(mutation => {
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === 1) { // Element node
                        const videos = node.tagName === 'VIDEO' ? [node] : node.querySelectorAll('video');
                        videos.forEach(video => this.optimizeVideoElement(video));
                    }
                });
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    optimizeVideoElement(video) {
        // Enable hardware acceleration
        video.style.transform = 'translateZ(0)';
        
        // Optimize loading
        if (!video.hasAttribute('preload')) {
            video.preload = 'metadata';
        }
        
        // Enable playsinline for mobile
        video.setAttribute('playsinline', '');
        
        // Optimize for mobile
        if (this.isMobile()) {
            video.setAttribute('webkit-playsinline', '');
            video.muted = true; // Required for autoplay on mobile
        }
    }
    
    // Network Optimization
    optimizeNetworkRequests() {
        // Use Service Worker for caching if available
        if ('serviceWorker' in navigator) {
            this.registerServiceWorker();
        }
        
        // Implement adaptive bitrate for better performance
        this.setupAdaptiveBitrate();
    }
    
    registerServiceWorker() {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => {
                console.log('Service Worker registered:', registration);
            })
            .catch(error => {
                console.log('Service Worker registration failed:', error);
            });
    }
    
    setupAdaptiveBitrate() {
        // Monitor network conditions
        if ('connection' in navigator) {
            const connection = navigator.connection;
            
            // Adjust video quality based on connection
            const adjustQuality = () => {
                const effectiveType = connection.effectiveType;
                let quality = 'auto';
                
                switch (effectiveType) {
                    case 'slow-2g':
                    case '2g':
                        quality = '360p';
                        break;
                    case '3g':
                        quality = '480p';
                        break;
                    case '4g':
                        quality = '720p';
                        break;
                    default:
                        quality = 'auto';
                }
                
                // Apply quality to all video players
                document.querySelectorAll('.mvp-quality-select').forEach(select => {
                    select.value = quality;
                    select.dispatchEvent(new Event('change'));
                });
            };
            
            connection.addEventListener('change', adjustQuality);
            adjustQuality(); // Initial setup
        }
    }
    
    // Memory Management
    optimizeMemoryUsage() {
        // Clean up unused video elements
        setInterval(() => {
            this.cleanupUnusedVideos();
        }, 30000); // Every 30 seconds
        
        // Handle page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseAllVideos();
            }
        });
    }
    
    cleanupUnusedVideos() {
        const videos = document.querySelectorAll('video');
        videos.forEach(video => {
            const rect = video.getBoundingClientRect();
            const isVisible = rect.top < window.innerHeight && rect.bottom > 0;
            
            if (!isVisible && video.readyState > 0) {
                // Unload video data to free memory
                video.removeAttribute('src');
                video.load();
            }
        });
    }
    
    pauseAllVideos() {
        document.querySelectorAll('video').forEach(video => {
            if (!video.paused) {
                video.pause();
            }
        });
    }
    
    // Utility methods
    isMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }
    
    isSlowConnection() {
        if ('connection' in navigator) {
            const connection = navigator.connection;
            return connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g';
        }
        return false;
    }
    
    // Performance monitoring
    monitorPerformance() {
        if ('PerformanceObserver' in window) {
            // Monitor paint timing
            const paintObserver = new PerformanceObserver((list) => {
                list.getEntries().forEach(entry => {
                    console.log(`${entry.name}: ${entry.startTime}ms`);
                });
            });
            paintObserver.observe({ entryTypes: ['paint'] });
            
            // Monitor layout shifts
            const layoutObserver = new PerformanceObserver((list) => {
                list.getEntries().forEach(entry => {
                    if (entry.value > 0.1) { // Significant layout shift
                        console.warn('Layout shift detected:', entry.value);
                    }
                });
            });
            layoutObserver.observe({ entryTypes: ['layout-shift'] });
        }
    }
}

// Initialize performance optimizations
const videoOptimizer = new VideoPerformanceOptimizer();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = VideoPerformanceOptimizer;
}

// Global access
window.VideoPerformanceOptimizer = VideoPerformanceOptimizer;
