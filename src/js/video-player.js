/**
 * Modern Video Player Library
 * Supports regular videos and YouTube with modern UI and performance optimizations
 */

class ModernVideoPlayer {
    constructor(container, options = {}) {
        this.container = container;
        this.options = {
            autoplay: false,
            muted: false,
            controls: true,
            preload: 'metadata',
            playsinline: true,
            poster: null,
            src: null,
            youtubeId: null,
            ...options
        };
        
        this.isYouTube = !!this.options.youtubeId;
        this.isPlaying = false;
        this.isMuted = this.options.muted;
        this.volume = 1;
        this.currentTime = 0;
        this.duration = 0;
        this.isFullscreen = false;
        this.controlsVisible = false;
        this.controlsTimeout = null;
        
        this.init();
    }
    
    init() {
        this.createPlayerHTML();
        this.bindElements();
        this.setupEventListeners();
        this.setupKeyboardShortcuts();
        
        if (this.isYouTube) {
            this.initYouTube();
        } else {
            this.initRegularVideo();
        }
        
        this.updateUI();
    }
    
    createPlayerHTML() {
        const template = `
            <div class="mvp-container">
                <div class="mvp-video-wrapper">
                    ${!this.isYouTube ? `
                        <video class="mvp-video" 
                               preload="${this.options.preload}" 
                               playsinline="${this.options.playsinline}"
                               ${this.options.poster ? `poster="${this.options.poster}"` : ''}>
                            ${this.options.src ? `<source src="${this.options.src}" type="video/mp4">` : ''}
                            Your browser does not support the video tag.
                        </video>
                    ` : ''}
                    
                    <div class="mvp-youtube-wrapper">
                        <div class="mvp-youtube-player"></div>
                    </div>
                    
                    <div class="mvp-loading">
                        <div class="mvp-spinner"></div>
                    </div>
                    
                    <div class="mvp-play-overlay">
                        <button class="mvp-play-btn-large" aria-label="Play video">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M8 5v14l11-7z"/>
                            </svg>
                        </button>
                    </div>
                    
                    <div class="mvp-controls-overlay">
                        <div class="mvp-top-controls">
                            <div class="mvp-title"></div>
                            <button class="mvp-pip-btn" aria-label="Picture in Picture">
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M19 7h-8v6h8V7zm2-4H3c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H3V5h18v14z"/>
                                </svg>
                            </button>
                        </div>
                        
                        <div class="mvp-bottom-controls">
                            <div class="mvp-progress-container">
                                <div class="mvp-progress-bar">
                                    <div class="mvp-progress-buffer"></div>
                                    <div class="mvp-progress-played"></div>
                                    <div class="mvp-progress-handle"></div>
                                </div>
                                <div class="mvp-time-tooltip">
                                    <div class="mvp-time-tooltip-content">00:00</div>
                                </div>
                            </div>
                            
                            <div class="mvp-controls-row">
                                <div class="mvp-controls-left">
                                    <button class="mvp-play-btn" aria-label="Play/Pause">
                                        <svg class="mvp-play-icon" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M8 5v14l11-7z"/>
                                        </svg>
                                        <svg class="mvp-pause-icon" viewBox="0 0 24 24" fill="currentColor" style="display: none;">
                                            <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                                        </svg>
                                    </button>
                                    
                                    <div class="mvp-volume-container">
                                        <button class="mvp-volume-btn" aria-label="Mute/Unmute">
                                            <svg class="mvp-volume-high" viewBox="0 0 24 24" fill="currentColor">
                                                <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
                                            </svg>
                                            <svg class="mvp-volume-muted" viewBox="0 0 24 24" fill="currentColor" style="display: none;">
                                                <path d="M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z"/>
                                            </svg>
                                        </button>
                                        <div class="mvp-volume-slider">
                                            <div class="mvp-volume-bar">
                                                <div class="mvp-volume-fill"></div>
                                                <div class="mvp-volume-handle"></div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mvp-time-display">
                                        <span class="mvp-current-time">00:00</span>
                                        <span class="mvp-time-separator">/</span>
                                        <span class="mvp-duration">00:00</span>
                                    </div>
                                </div>
                                
                                <div class="mvp-controls-right">
                                    <button class="mvp-settings-btn" aria-label="Settings">
                                        <svg viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
                                        </svg>
                                    </button>
                                    
                                    <button class="mvp-fullscreen-btn" aria-label="Fullscreen">
                                        <svg class="mvp-fullscreen-enter" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"/>
                                        </svg>
                                        <svg class="mvp-fullscreen-exit" viewBox="0 0 24 24" fill="currentColor" style="display: none;">
                                            <path d="M5 16h3v3h2v-5H5v2zm3-8H5v2h5V5H8v3zm6 11h2v-3h3v-2h-5v5zm2-11V5h-2v5h5V8h-3z"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mvp-settings-menu" style="display: none;">
                    <div class="mvp-settings-content">
                        <div class="mvp-settings-item">
                            <label>Playback Speed</label>
                            <select class="mvp-speed-select">
                                <option value="0.25">0.25x</option>
                                <option value="0.5">0.5x</option>
                                <option value="0.75">0.75x</option>
                                <option value="1" selected>Normal</option>
                                <option value="1.25">1.25x</option>
                                <option value="1.5">1.5x</option>
                                <option value="2">2x</option>
                            </select>
                        </div>
                        <div class="mvp-settings-item">
                            <label>Quality</label>
                            <select class="mvp-quality-select">
                                <option value="auto" selected>Auto</option>
                                <option value="1080p">1080p</option>
                                <option value="720p">720p</option>
                                <option value="480p">480p</option>
                                <option value="360p">360p</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        this.container.innerHTML = template;
    }
    
    bindElements() {
        this.playerContainer = this.container.querySelector('.mvp-container');
        this.video = this.container.querySelector('.mvp-video');
        this.youtubeWrapper = this.container.querySelector('.mvp-youtube-wrapper');
        this.youtubePlayer = this.container.querySelector('.mvp-youtube-player');
        this.loading = this.container.querySelector('.mvp-loading');
        this.playOverlay = this.container.querySelector('.mvp-play-overlay');
        this.playBtnLarge = this.container.querySelector('.mvp-play-btn-large');
        this.controlsOverlay = this.container.querySelector('.mvp-controls-overlay');
        this.playBtn = this.container.querySelector('.mvp-play-btn');
        this.playIcon = this.container.querySelector('.mvp-play-icon');
        this.pauseIcon = this.container.querySelector('.mvp-pause-icon');
        this.volumeBtn = this.container.querySelector('.mvp-volume-btn');
        this.volumeHigh = this.container.querySelector('.mvp-volume-high');
        this.volumeMuted = this.container.querySelector('.mvp-volume-muted');
        this.volumeBar = this.container.querySelector('.mvp-volume-bar');
        this.volumeFill = this.container.querySelector('.mvp-volume-fill');
        this.volumeHandle = this.container.querySelector('.mvp-volume-handle');
        this.progressContainer = this.container.querySelector('.mvp-progress-container');
        this.progressBar = this.container.querySelector('.mvp-progress-bar');
        this.progressBuffer = this.container.querySelector('.mvp-progress-buffer');
        this.progressPlayed = this.container.querySelector('.mvp-progress-played');
        this.progressHandle = this.container.querySelector('.mvp-progress-handle');
        this.timeTooltip = this.container.querySelector('.mvp-time-tooltip');
        this.timeTooltipContent = this.container.querySelector('.mvp-time-tooltip-content');
        this.currentTimeEl = this.container.querySelector('.mvp-current-time');
        this.durationEl = this.container.querySelector('.mvp-duration');
        this.fullscreenBtn = this.container.querySelector('.mvp-fullscreen-btn');
        this.fullscreenEnter = this.container.querySelector('.mvp-fullscreen-enter');
        this.fullscreenExit = this.container.querySelector('.mvp-fullscreen-exit');
        this.settingsBtn = this.container.querySelector('.mvp-settings-btn');
        this.settingsMenu = this.container.querySelector('.mvp-settings-menu');
        this.speedSelect = this.container.querySelector('.mvp-speed-select');
        this.pipBtn = this.container.querySelector('.mvp-pip-btn');
    }

    setupEventListeners() {
        // Play/Pause events
        this.playBtnLarge.addEventListener('click', () => this.togglePlay());
        this.playBtn.addEventListener('click', () => this.togglePlay());

        // Volume events
        this.volumeBtn.addEventListener('click', () => this.toggleMute());
        this.volumeBar.addEventListener('click', (e) => this.handleVolumeClick(e));
        this.volumeHandle.addEventListener('mousedown', (e) => this.handleVolumeMouseDown(e));

        // Progress events
        this.progressBar.addEventListener('click', (e) => this.handleProgressClick(e));
        this.progressBar.addEventListener('mousemove', (e) => this.handleProgressMouseMove(e));
        this.progressBar.addEventListener('mouseleave', () => this.hideTimeTooltip());
        this.progressHandle.addEventListener('mousedown', (e) => this.handleProgressMouseDown(e));

        // Fullscreen events
        this.fullscreenBtn.addEventListener('click', () => this.toggleFullscreen());
        document.addEventListener('fullscreenchange', () => this.handleFullscreenChange());

        // Settings events
        this.settingsBtn.addEventListener('click', () => this.toggleSettings());
        this.speedSelect.addEventListener('change', (e) => this.changePlaybackSpeed(e.target.value));

        // PiP events
        this.pipBtn.addEventListener('click', () => this.togglePictureInPicture());

        // Controls visibility
        this.playerContainer.addEventListener('mouseenter', () => this.showControls());
        this.playerContainer.addEventListener('mouseleave', () => this.hideControls());
        this.playerContainer.addEventListener('mousemove', () => this.showControls());

        // Simple click to play/pause
        this.playerContainer.addEventListener('click', (e) => {
            console.log('Click detected on:', e.target.tagName, e.target.className);

            // Skip if clicking on controls or buttons
            if (e.target.tagName === 'BUTTON' ||
                e.target.closest('button') ||
                e.target.closest('.mvp-controls-row') ||
                e.target.closest('.mvp-progress-container') ||
                e.target.closest('.mvp-settings-menu') ||
                e.target.closest('.mvp-controls-overlay')) {
                console.log('Click on controls, ignoring');
                return;
            }

            console.log('Video area clicked, current state:', this.isPlaying ? 'playing' : 'paused');
            console.log('Toggling play state...');
            this.togglePlay();
        });

        // Video events (for regular video)
        if (this.video) {
            this.video.addEventListener('loadedmetadata', () => this.handleLoadedMetadata());
            this.video.addEventListener('timeupdate', () => this.handleTimeUpdate());
            this.video.addEventListener('progress', () => this.handleProgress());
            this.video.addEventListener('play', () => this.handlePlay());
            this.video.addEventListener('pause', () => this.handlePause());
            this.video.addEventListener('ended', () => this.handleEnded());
            this.video.addEventListener('waiting', () => this.showLoading());
            this.video.addEventListener('canplay', () => this.hideLoading());
        }
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (!this.playerContainer.matches(':hover')) return;

            switch (e.code) {
                case 'Space':
                    e.preventDefault();
                    this.togglePlay();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    this.seek(this.currentTime - 10);
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    this.seek(this.currentTime + 10);
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    this.setVolume(Math.min(1, this.volume + 0.1));
                    break;
                case 'ArrowDown':
                    e.preventDefault();
                    this.setVolume(Math.max(0, this.volume - 0.1));
                    break;
                case 'KeyM':
                    e.preventDefault();
                    this.toggleMute();
                    break;
                case 'KeyF':
                    e.preventDefault();
                    this.toggleFullscreen();
                    break;
            }
        });
    }

    initRegularVideo() {
        if (!this.video || !this.options.src) return;

        this.video.src = this.options.src;
        this.video.muted = this.options.muted;

        if (this.options.autoplay) {
            this.video.autoplay = true;
            this.video.muted = true; // Required for autoplay
        }

        this.hideLoading();
    }

    initYouTube() {
        if (!this.options.youtubeId) return;

        console.log('Initializing YouTube player for ID:', this.options.youtubeId);

        // Show YouTube wrapper
        this.youtubeWrapper.style.display = 'block';

        // Load YouTube API if not already loaded
        if (!window.YT || !window.YT.Player) {
            console.log('YouTube API not loaded, loading...');
            this.loadYouTubeAPI();
        } else {
            console.log('YouTube API already loaded, creating player...');
            this.createYouTubePlayer();
        }
    }

    loadYouTubeAPI() {
        // Check if script is already loading
        if (document.querySelector('script[src*="youtube.com/iframe_api"]')) {
            // Script already exists, wait for it to load
            this.waitForYouTubeAPI();
            return;
        }

        const script = document.createElement('script');
        script.src = 'https://www.youtube.com/iframe_api';
        document.head.appendChild(script);

        // Store reference to this player for callback
        if (!window.youtubePlayersWaiting) {
            window.youtubePlayersWaiting = [];
        }
        window.youtubePlayersWaiting.push(this);

        // Set up global callback only once
        if (!window.onYouTubeIframeAPIReady) {
            window.onYouTubeIframeAPIReady = () => {
                console.log('YouTube API ready, initializing players...');
                if (window.youtubePlayersWaiting) {
                    window.youtubePlayersWaiting.forEach(player => {
                        player.createYouTubePlayer();
                    });
                    window.youtubePlayersWaiting = [];
                }
            };
        }
    }

    waitForYouTubeAPI() {
        // Poll for YouTube API availability
        const checkAPI = () => {
            if (window.YT && window.YT.Player) {
                this.createYouTubePlayer();
            } else {
                setTimeout(checkAPI, 100);
            }
        };
        checkAPI();
    }

    createYouTubePlayer() {
        console.log('Creating YouTube player for video ID:', this.options.youtubeId);
        this.showLoading();

        // Create unique ID for YouTube player
        const playerId = 'youtube-player-' + Math.random().toString(36).substr(2, 9);
        this.youtubePlayer.id = playerId;

        try {
            this.ytPlayer = new YT.Player(playerId, {
                height: '100%',
                width: '100%',
                videoId: this.options.youtubeId,
                playerVars: {
                    autoplay: this.options.autoplay ? 1 : 0,
                    controls: 0,
                    disablekb: 1,
                    fs: 0,
                    modestbranding: 1,
                    playsinline: 1,
                    rel: 0,
                    iv_load_policy: 3,
                    enablejsapi: 1,
                    origin: window.location.origin
                },
                events: {
                    onReady: (event) => this.handleYouTubeReady(event),
                    onStateChange: (event) => this.handleYouTubeStateChange(event),
                    onError: (event) => this.handleYouTubeError(event)
                }
            });

            console.log('YouTube player created successfully with ID:', playerId);
        } catch (error) {
            console.error('Error creating YouTube player:', error);
            this.hideLoading();
        }
    }

    handleYouTubeError(event) {
        console.error('YouTube player error:', event.data);
        this.hideLoading();

        // Show error message
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            text-align: center;
            background: rgba(0,0,0,0.8);
            padding: 20px;
            border-radius: 8px;
        `;
        errorDiv.innerHTML = `
            <h3>YouTube Video Error</h3>
            <p>Unable to load video. Please check the video ID.</p>
        `;
        this.youtubeWrapper.appendChild(errorDiv);
    }

    handleYouTubeReady(event) {
        console.log('YouTube player ready');

        try {
            this.duration = this.ytPlayer.getDuration();
            this.updateDurationDisplay();
            this.hideLoading();

            // Show play overlay initially
            this.showPlayOverlay();

            // Start time update interval
            this.ytTimeUpdateInterval = setInterval(() => {
                if (this.ytPlayer && this.ytPlayer.getCurrentTime) {
                    this.currentTime = this.ytPlayer.getCurrentTime();
                    this.handleTimeUpdate();
                }
            }, 100);

            console.log('YouTube player initialized successfully');
        } catch (error) {
            console.error('Error in handleYouTubeReady:', error);
            this.hideLoading();
        }
    }

    handleYouTubeStateChange(event) {
        switch (event.data) {
            case YT.PlayerState.PLAYING:
                this.handlePlay();
                this.hideLoading();
                break;
            case YT.PlayerState.PAUSED:
                this.handlePause();
                this.hideLoading();
                break;
            case YT.PlayerState.ENDED:
                this.handleEnded();
                this.hideLoading();
                break;
            case YT.PlayerState.BUFFERING:
                this.showLoading();
                break;
            case YT.PlayerState.CUED:
                this.hideLoading();
                break;
            default:
                this.hideLoading();
        }
    }

    // Event handlers
    handleLoadedMetadata() {
        this.duration = this.video.duration;
        this.updateDurationDisplay();
    }

    handleTimeUpdate() {
        if (this.isYouTube) {
            this.currentTime = this.ytPlayer.getCurrentTime();
        } else {
            this.currentTime = this.video.currentTime;
        }
        this.updateProgressBar();
        this.updateCurrentTimeDisplay();
    }

    handleProgress() {
        if (this.video && this.video.buffered.length > 0) {
            const buffered = this.video.buffered.end(this.video.buffered.length - 1);
            const progress = (buffered / this.duration) * 100;
            this.progressBuffer.style.width = `${progress}%`;
        }
    }

    handlePlay() {
        this.isPlaying = true;
        this.updatePlayButton();
        this.hidePlayOverlay();

        // Dispatch custom event
        this.container.dispatchEvent(new CustomEvent('mvp:play', {
            detail: { currentTime: this.currentTime }
        }));
    }

    handlePause() {
        this.isPlaying = false;
        this.updatePlayButton();
        this.showPlayOverlay();

        // Dispatch custom event
        this.container.dispatchEvent(new CustomEvent('mvp:pause', {
            detail: { currentTime: this.currentTime }
        }));
    }

    handleEnded() {
        this.isPlaying = false;
        this.updatePlayButton();
        this.showPlayOverlay();

        // Dispatch custom event
        this.container.dispatchEvent(new CustomEvent('mvp:ended', {
            detail: { currentTime: this.currentTime }
        }));
    }

    // Control methods
    togglePlay() {
        console.log('togglePlay called, current state:', this.isPlaying ? 'playing' : 'paused');
        if (this.isPlaying) {
            console.log('Pausing video...');
            this.pause();
        } else {
            console.log('Playing video...');
            this.play();
        }
    }

    play() {
        console.log('play() called, isYouTube:', this.isYouTube);
        if (this.isYouTube && this.ytPlayer) {
            console.log('Playing YouTube video');
            this.ytPlayer.playVideo();
        } else if (this.video) {
            console.log('Playing regular video');
            this.video.play();
        }
    }

    pause() {
        console.log('pause() called, isYouTube:', this.isYouTube);
        if (this.isYouTube && this.ytPlayer) {
            console.log('Pausing YouTube video');
            this.ytPlayer.pauseVideo();
        } else if (this.video) {
            console.log('Pausing regular video');
            this.video.pause();
        }
    }

    seek(time) {
        time = Math.max(0, Math.min(time, this.duration));

        if (this.isYouTube && this.ytPlayer) {
            this.ytPlayer.seekTo(time);
        } else if (this.video) {
            this.video.currentTime = time;
        }

        this.currentTime = time;
        this.updateProgressBar();
        this.updateCurrentTimeDisplay();
    }

    setVolume(volume) {
        this.volume = Math.max(0, Math.min(1, volume));

        if (this.isYouTube && this.ytPlayer) {
            this.ytPlayer.setVolume(this.volume * 100);
        } else if (this.video) {
            this.video.volume = this.volume;
        }

        this.updateVolumeBar();
        this.updateVolumeButton();
    }

    toggleMute() {
        this.isMuted = !this.isMuted;

        if (this.isYouTube && this.ytPlayer) {
            if (this.isMuted) {
                this.ytPlayer.mute();
            } else {
                this.ytPlayer.unMute();
            }
        } else if (this.video) {
            this.video.muted = this.isMuted;
        }

        this.updateVolumeButton();
    }

    toggleFullscreen() {
        if (!this.isFullscreen) {
            this.enterFullscreen();
        } else {
            this.exitFullscreen();
        }
    }

    enterFullscreen() {
        if (this.playerContainer.requestFullscreen) {
            this.playerContainer.requestFullscreen();
        } else if (this.playerContainer.webkitRequestFullscreen) {
            this.playerContainer.webkitRequestFullscreen();
        } else if (this.playerContainer.msRequestFullscreen) {
            this.playerContainer.msRequestFullscreen();
        }
    }

    exitFullscreen() {
        if (document.exitFullscreen) {
            document.exitFullscreen();
        } else if (document.webkitExitFullscreen) {
            document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) {
            document.msExitFullscreen();
        }
    }

    handleFullscreenChange() {
        this.isFullscreen = !!(document.fullscreenElement ||
                              document.webkitFullscreenElement ||
                              document.msFullscreenElement);
        this.updateFullscreenButton();
    }

    togglePictureInPicture() {
        if (!this.video || this.isYouTube) return;

        if (document.pictureInPictureElement) {
            document.exitPictureInPicture();
        } else {
            this.video.requestPictureInPicture();
        }
    }

    toggleSettings() {
        const isVisible = this.settingsMenu.style.display !== 'none';
        this.settingsMenu.style.display = isVisible ? 'none' : 'block';
    }

    changePlaybackSpeed(speed) {
        const rate = parseFloat(speed);

        if (this.isYouTube && this.ytPlayer) {
            this.ytPlayer.setPlaybackRate(rate);
        } else if (this.video) {
            this.video.playbackRate = rate;
        }
    }

    // Mouse event handlers
    handleProgressClick(e) {
        const rect = this.progressBar.getBoundingClientRect();
        const percent = (e.clientX - rect.left) / rect.width;
        const time = percent * this.duration;
        this.seek(time);
    }

    handleProgressMouseMove(e) {
        const rect = this.progressBar.getBoundingClientRect();
        const percent = (e.clientX - rect.left) / rect.width;
        const time = percent * this.duration;

        this.timeTooltipContent.textContent = this.formatTime(time);
        this.timeTooltip.style.left = `${percent * 100}%`;
        this.showTimeTooltip();
    }

    handleProgressMouseDown(e) {
        this.isDraggingProgress = true;
        document.addEventListener('mousemove', this.handleProgressDrag.bind(this));
        document.addEventListener('mouseup', this.handleProgressDragEnd.bind(this));
        e.preventDefault();
    }

    handleProgressDrag(e) {
        if (!this.isDraggingProgress) return;

        const rect = this.progressBar.getBoundingClientRect();
        const percent = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width));
        const time = percent * this.duration;
        this.seek(time);
    }

    handleProgressDragEnd() {
        this.isDraggingProgress = false;
        document.removeEventListener('mousemove', this.handleProgressDrag);
        document.removeEventListener('mouseup', this.handleProgressDragEnd);
    }

    handleVolumeClick(e) {
        const rect = this.volumeBar.getBoundingClientRect();
        const percent = (e.clientX - rect.left) / rect.width;
        this.setVolume(percent);
    }

    handleVolumeMouseDown(e) {
        this.isDraggingVolume = true;
        document.addEventListener('mousemove', this.handleVolumeDrag.bind(this));
        document.addEventListener('mouseup', this.handleVolumeDragEnd.bind(this));
        e.preventDefault();
    }

    handleVolumeDrag(e) {
        if (!this.isDraggingVolume) return;

        const rect = this.volumeBar.getBoundingClientRect();
        const percent = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width));
        this.setVolume(percent);
    }

    handleVolumeDragEnd() {
        this.isDraggingVolume = false;
        document.removeEventListener('mousemove', this.handleVolumeDrag);
        document.removeEventListener('mouseup', this.handleVolumeDragEnd);
    }

    // UI update methods
    updateUI() {
        this.updatePlayButton();
        this.updateVolumeButton();
        this.updateVolumeBar();
        this.updateProgressBar();
        this.updateCurrentTimeDisplay();
        this.updateDurationDisplay();
        this.updateFullscreenButton();
    }

    updatePlayButton() {
        if (this.isPlaying) {
            this.playIcon.style.display = 'none';
            this.pauseIcon.style.display = 'block';
        } else {
            this.playIcon.style.display = 'block';
            this.pauseIcon.style.display = 'none';
        }
    }

    updateVolumeButton() {
        if (this.isMuted || this.volume === 0) {
            this.volumeHigh.style.display = 'none';
            this.volumeMuted.style.display = 'block';
        } else {
            this.volumeHigh.style.display = 'block';
            this.volumeMuted.style.display = 'none';
        }
    }

    updateVolumeBar() {
        const percent = this.isMuted ? 0 : this.volume * 100;
        this.volumeFill.style.width = `${percent}%`;
        this.volumeHandle.style.left = `${percent}%`;
    }

    updateProgressBar() {
        if (this.duration > 0) {
            const percent = (this.currentTime / this.duration) * 100;
            this.progressPlayed.style.width = `${percent}%`;
            this.progressHandle.style.left = `${percent}%`;
        }
    }

    updateCurrentTimeDisplay() {
        this.currentTimeEl.textContent = this.formatTime(this.currentTime);
    }

    updateDurationDisplay() {
        this.durationEl.textContent = this.formatTime(this.duration);
    }

    updateFullscreenButton() {
        if (this.isFullscreen) {
            this.fullscreenEnter.style.display = 'none';
            this.fullscreenExit.style.display = 'block';
        } else {
            this.fullscreenEnter.style.display = 'block';
            this.fullscreenExit.style.display = 'none';
        }
    }

    // Controls visibility
    showControls() {
        this.controlsOverlay.classList.add('visible');
        this.clearControlsTimeout();
        this.controlsTimeout = setTimeout(() => this.hideControls(), 3000);
    }

    hideControls() {
        if (!this.isPlaying) return;
        this.controlsOverlay.classList.remove('visible');
        this.clearControlsTimeout();
    }

    clearControlsTimeout() {
        if (this.controlsTimeout) {
            clearTimeout(this.controlsTimeout);
            this.controlsTimeout = null;
        }
    }

    showPlayOverlay() {
        this.playOverlay.classList.remove('hidden');
    }

    hidePlayOverlay() {
        this.playOverlay.classList.add('hidden');
    }

    showLoading() {
        this.loading.style.display = 'block';
    }

    hideLoading() {
        this.loading.style.display = 'none';
    }

    showTimeTooltip() {
        this.timeTooltip.classList.add('visible');
    }

    hideTimeTooltip() {
        this.timeTooltip.classList.remove('visible');
    }

    // Utility methods
    formatTime(seconds) {
        if (isNaN(seconds)) return '00:00';

        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);

        if (hours > 0) {
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
    }

    // Performance optimizations
    enableGPUAcceleration() {
        if (this.video) {
            this.video.style.transform = 'translateZ(0)';
            this.video.style.willChange = 'transform';
        }
    }

    preloadVideo() {
        if (this.video && this.options.src) {
            this.video.preload = 'auto';
        }
    }

    // Lazy loading
    static observeVideos() {
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const container = entry.target;

                        // Skip if already initialized
                        if (container.dataset.initialized) return;

                        const src = container.dataset.src;
                        const youtubeId = container.dataset.youtubeId;

                        if (src || youtubeId) {
                            const options = {
                                src: src,
                                youtubeId: youtubeId,
                                autoplay: container.dataset.autoplay === 'true',
                                muted: container.dataset.muted === 'true',
                                poster: container.dataset.poster
                            };

                            new ModernVideoPlayer(container, options);
                            container.dataset.initialized = 'true';
                            observer.unobserve(container);
                        }
                    }
                });
            }, {
                rootMargin: '100px', // Load earlier for better UX
                threshold: 0.1
            });

            // Only observe lazy loading videos
            document.querySelectorAll('.modern-video-player[data-lazy="true"]').forEach(container => {
                if (!container.dataset.initialized) {
                    observer.observe(container);
                }
            });
        } else {
            // Fallback for browsers without IntersectionObserver
            ModernVideoPlayer.initAll();
        }
    }

    // Static methods
    static initAll() {
        // Only initialize non-lazy videos
        document.querySelectorAll('.modern-video-player:not([data-lazy="true"])').forEach(container => {
            if (container.dataset.initialized) return;

            const options = {
                src: container.dataset.src,
                youtubeId: container.dataset.youtubeId,
                autoplay: container.dataset.autoplay === 'true',
                muted: container.dataset.muted === 'true',
                poster: container.dataset.poster
            };

            new ModernVideoPlayer(container, options);
            container.dataset.initialized = 'true';
        });
    }

    static init(selector, options = {}) {
        const container = document.querySelector(selector);
        if (container) {
            return new ModernVideoPlayer(container, options);
        }
        return null;
    }

    // Cleanup
    destroy() {
        // Clear intervals
        if (this.ytTimeUpdateInterval) {
            clearInterval(this.ytTimeUpdateInterval);
        }

        if (this.controlsTimeout) {
            clearTimeout(this.controlsTimeout);
        }

        // Remove event listeners
        document.removeEventListener('mousemove', this.handleProgressDrag);
        document.removeEventListener('mouseup', this.handleProgressDragEnd);
        document.removeEventListener('mousemove', this.handleVolumeDrag);
        document.removeEventListener('mouseup', this.handleVolumeDragEnd);

        // Destroy YouTube player
        if (this.ytPlayer && this.ytPlayer.destroy) {
            this.ytPlayer.destroy();
        }

        // Clear container
        this.container.innerHTML = '';
    }
}

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Check if there are any lazy loading videos
    const lazyVideos = document.querySelectorAll('.modern-video-player[data-lazy="true"]');
    const immediateVideos = document.querySelectorAll('.modern-video-player:not([data-lazy="true"])');

    // Initialize immediate videos
    if (immediateVideos.length > 0) {
        ModernVideoPlayer.initAll();
    }

    // Setup lazy loading for lazy videos
    if (lazyVideos.length > 0) {
        ModernVideoPlayer.observeVideos();
    }
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ModernVideoPlayer;
}

// Global access
window.ModernVideoPlayer = ModernVideoPlayer;
