/**
 * Cross-Browser Compatibility and Autoplay Settings
 * Ensures video player works across Chrome, Firefox, Safari, Edge
 */

class BrowserCompatibility {
    constructor() {
        this.browser = this.detectBrowser();
        this.init();
    }

    init() {
        this.setupAutoplayPolicies();
        this.setupFullscreenAPI();
        this.setupPictureInPictureAPI();
        this.setupMediaSessionAPI();
        this.setupVideoFormats();
        this.setupTouchEvents();
        this.setupKeyboardEvents();
    }

    // Browser Detection
    detectBrowser() {
        const userAgent = navigator.userAgent;

        if (userAgent.includes('Chrome') && !userAgent.includes('Edg')) {
            return 'chrome';
        } else if (userAgent.includes('Firefox')) {
            return 'firefox';
        } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
            return 'safari';
        } else if (userAgent.includes('Edg')) {
            return 'edge';
        }

        return 'unknown';
    }

    // Autoplay Policies
    setupAutoplayPolicies() {
        // Modern browsers require user interaction or muted videos for autoplay
        this.autoplayPolicy = {
            chrome: {
                requiresUserInteraction: true,
                allowsMutedAutoplay: true,
                requiresVisibleVideo: true
            },
            firefox: {
                requiresUserInteraction: false,
                allowsMutedAutoplay: true,
                requiresVisibleVideo: false
            },
            safari: {
                requiresUserInteraction: true,
                allowsMutedAutoplay: true,
                requiresVisibleVideo: true
            },
            edge: {
                requiresUserInteraction: true,
                allowsMutedAutoplay: true,
                requiresVisibleVideo: true
            }
        };
    }

    canAutoplay(video, options = {}) {
        const policy = this.autoplayPolicy[this.browser] || this.autoplayPolicy.chrome;

        // Check if muted autoplay is allowed
        if (options.muted && policy.allowsMutedAutoplay) {
            return true;
        }

        // Check if user interaction is required
        if (policy.requiresUserInteraction && !this.hasUserInteracted()) {
            return false;
        }

        // Check if video needs to be visible
        if (policy.requiresVisibleVideo && !this.isVideoVisible(video)) {
            return false;
        }

        return true;
    }

    hasUserInteracted() {
        // Check if user has interacted with the page
        return document.hasStoredUserActivation ||
               sessionStorage.getItem('userInteracted') === 'true';
    }

    isVideoVisible(video) {
        const rect = video.getBoundingClientRect();
        return rect.top < window.innerHeight && rect.bottom > 0;
    }

    // Fullscreen API Compatibility
    setupFullscreenAPI() {
        this.fullscreenAPI = {
            requestFullscreen: this.getFullscreenMethod('requestFullscreen'),
            exitFullscreen: this.getFullscreenMethod('exitFullscreen'),
            fullscreenElement: this.getFullscreenProperty('fullscreenElement'),
            fullscreenEnabled: this.getFullscreenProperty('fullscreenEnabled'),
            fullscreenchange: this.getFullscreenEvent('fullscreenchange'),
            fullscreenerror: this.getFullscreenEvent('fullscreenerror')
        };
    }

    getFullscreenMethod(method) {
        const prefixes = ['', 'webkit', 'moz', 'ms'];

        for (const prefix of prefixes) {
            const fullMethod = prefix + method.charAt(0).toUpperCase() + method.slice(1);
            if (document[fullMethod] || Element.prototype[fullMethod]) {
                return fullMethod;
            }
        }

        return null;
    }

    getFullscreenProperty(property) {
        const prefixes = ['', 'webkit', 'moz', 'ms'];

        for (const prefix of prefixes) {
            const fullProperty = prefix + property.charAt(0).toUpperCase() + property.slice(1);
            if (fullProperty in document) {
                return fullProperty;
            }
        }

        return null;
    }

    getFullscreenEvent(event) {
        const prefixes = ['', 'webkit', 'moz', 'ms'];

        for (const prefix of prefixes) {
            const fullEvent = prefix + event;
            return fullEvent;
        }

        return event;
    }

    // Picture-in-Picture API
    setupPictureInPictureAPI() {
        this.pipSupported = 'pictureInPictureEnabled' in document;

        // Safari uses webkitSetPresentationMode
        if (!this.pipSupported && 'webkitSetPresentationMode' in HTMLVideoElement.prototype) {
            this.pipSupported = true;
            this.pipMethod = 'webkit';
        }
    }

    requestPictureInPicture(video) {
        if (!this.pipSupported) return Promise.reject('PiP not supported');

        if (this.pipMethod === 'webkit') {
            // Safari implementation
            video.webkitSetPresentationMode('picture-in-picture');
            return Promise.resolve();
        } else {
            // Standard implementation
            return video.requestPictureInPicture();
        }
    }

    exitPictureInPicture() {
        if (!this.pipSupported) return Promise.reject('PiP not supported');

        if (this.pipMethod === 'webkit') {
            // Safari implementation
            const video = document.querySelector('video[webkit-presentation-mode="picture-in-picture"]');
            if (video) {
                video.webkitSetPresentationMode('inline');
            }
            return Promise.resolve();
        } else {
            // Standard implementation
            return document.exitPictureInPicture();
        }
    }

    // Media Session API
    setupMediaSessionAPI() {
        if ('mediaSession' in navigator) {
            this.mediaSessionSupported = true;
        }
    }

    updateMediaSession(metadata) {
        if (!this.mediaSessionSupported) return;

        navigator.mediaSession.metadata = new MediaMetadata(metadata);

        // Set action handlers
        const actions = ['play', 'pause', 'seekbackward', 'seekforward'];

        actions.forEach(action => {
            try {
                navigator.mediaSession.setActionHandler(action, (details) => {
                    this.handleMediaSessionAction(action, details);
                });
            } catch (error) {
                console.log(`Media Session action ${action} not supported`);
            }
        });
    }

    handleMediaSessionAction(action, details) {
        const event = new CustomEvent('mediasession', {
            detail: { action, details }
        });
        document.dispatchEvent(event);
    }

    // Video Format Support
    setupVideoFormats() {
        this.supportedFormats = this.detectSupportedFormats();
    }

    detectSupportedFormats() {
        const video = document.createElement('video');
        const formats = {
            mp4: video.canPlayType('video/mp4'),
            webm: video.canPlayType('video/webm'),
            ogg: video.canPlayType('video/ogg'),
            hls: video.canPlayType('application/vnd.apple.mpegurl') ||
                 video.canPlayType('application/x-mpegURL'),
            dash: video.canPlayType('application/dash+xml')
        };

        return formats;
    }

    getBestVideoFormat(sources) {
        // Priority order based on browser support and quality
        const priority = ['mp4', 'webm', 'ogg'];

        for (const format of priority) {
            const source = sources.find(s => s.type.includes(format));
            if (source && this.supportedFormats[format]) {
                return source;
            }
        }

        return sources[0]; // Fallback to first source
    }

    // Touch Events for Mobile
    setupTouchEvents() {
        this.touchSupported = 'ontouchstart' in window;

        if (this.touchSupported) {
            this.setupMobileTouchHandlers();
        }
    }

    setupMobileTouchHandlers() {
        // Add touch-specific CSS
        const style = document.createElement('style');
        style.textContent = `
            @media (hover: none) and (pointer: coarse) {
                .mvp-controls-overlay {
                    opacity: 1;
                    pointer-events: auto;
                }

                .mvp-volume-slider {
                    width: 80px !important;
                }

                .mvp-progress-handle,
                .mvp-volume-handle {
                    opacity: 1;
                    width: 20px;
                    height: 20px;
                }

                .mvp-controls-overlay button {
                    min-width: 44px;
                    min-height: 44px;
                }
            }
        `;
        document.head.appendChild(style);
    }

    // Keyboard Events
    setupKeyboardEvents() {
        // Ensure keyboard events work across browsers
        this.keyboardEventSupported = true;

        // Check if code property is supported in a safer way
        try {
            const testEvent = new KeyboardEvent('keydown');
            if (!('code' in testEvent)) {
                this.polyfillKeyboardEvents();
            }
        } catch (error) {
            // Fallback for very old browsers
            this.polyfillKeyboardEvents();
        }
    }

    polyfillKeyboardEvents() {
        // Simple polyfill for older browsers
        const keyCodeMap = {
            32: 'Space',
            37: 'ArrowLeft',
            38: 'ArrowUp',
            39: 'ArrowRight',
            40: 'ArrowDown',
            70: 'KeyF',
            77: 'KeyM'
        };

        document.addEventListener('keydown', (e) => {
            if (!e.code && keyCodeMap[e.keyCode]) {
                e.code = keyCodeMap[e.keyCode];
            }
        });
    }

    // Autoplay Helper Methods
    attemptAutoplay(video, options = {}) {
        return new Promise((resolve, reject) => {
            if (!this.canAutoplay(video, options)) {
                reject('Autoplay not allowed');
                return;
            }

            // Set video properties for autoplay
            video.muted = options.muted || true;
            video.autoplay = true;

            // Attempt to play
            const playPromise = video.play();

            if (playPromise !== undefined) {
                playPromise
                    .then(() => resolve())
                    .catch((error) => {
                        // Autoplay failed, try muted autoplay
                        if (!video.muted) {
                            video.muted = true;
                            video.play()
                                .then(() => resolve())
                                .catch(() => reject(error));
                        } else {
                            reject(error);
                        }
                    });
            } else {
                // Older browsers without play() promise
                resolve();
            }
        });
    }

    // User Interaction Tracking
    trackUserInteraction() {
        const events = ['click', 'touchstart', 'keydown'];

        const handleInteraction = () => {
            sessionStorage.setItem('userInteracted', 'true');
            events.forEach(event => {
                document.removeEventListener(event, handleInteraction);
            });
        };

        events.forEach(event => {
            document.addEventListener(event, handleInteraction, { once: true });
        });
    }

    // Browser-specific fixes
    applyBrowserFixes() {
        switch (this.browser) {
            case 'safari':
                this.applySafariFixes();
                break;
            case 'firefox':
                this.applyFirefoxFixes();
                break;
            case 'edge':
                this.applyEdgeFixes();
                break;
            case 'chrome':
                this.applyChromeFixes();
                break;
        }
    }

    applySafariFixes() {
        // Safari-specific fixes
        const style = document.createElement('style');
        style.textContent = `
            .mvp-video {
                -webkit-transform: translateZ(0);
            }

            .mvp-container {
                -webkit-overflow-scrolling: touch;
            }
        `;
        document.head.appendChild(style);
    }

    applyFirefoxFixes() {
        // Firefox-specific fixes
        const style = document.createElement('style');
        style.textContent = `
            .mvp-video {
                -moz-transform: translateZ(0);
            }
        `;
        document.head.appendChild(style);
    }

    applyEdgeFixes() {
        // Edge-specific fixes
        const style = document.createElement('style');
        style.textContent = `
            .mvp-video {
                -ms-transform: translateZ(0);
            }
        `;
        document.head.appendChild(style);
    }

    applyChromeFixes() {
        // Chrome-specific fixes
        // Chrome generally has good support, minimal fixes needed
    }

    // Initialize compatibility
    static init() {
        const compatibility = new BrowserCompatibility();
        compatibility.trackUserInteraction();
        compatibility.applyBrowserFixes();
        return compatibility;
    }
}

// Auto-initialize only if not already initialized
if (typeof window !== 'undefined' && !window.browserCompatibilityInitialized) {
    window.browserCompatibilityInitialized = true;
    BrowserCompatibility.init();
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BrowserCompatibility;
}

// Global access
window.BrowserCompatibility = BrowserCompatibility;