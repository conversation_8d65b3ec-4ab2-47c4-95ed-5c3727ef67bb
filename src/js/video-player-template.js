// Video Player HTML Template
const VideoPlayerTemplate = {
    // Main video player template
    player: `
        <div class="mvp-container">
            <div class="mvp-video-wrapper">
                <video class="mvp-video" preload="metadata" playsinline>
                    <source src="" type="video/mp4">
                    Your browser does not support the video tag.
                </video>
                <div class="mvp-youtube-wrapper" style="display: none;">
                    <div class="mvp-youtube-player"></div>
                </div>
                
                <!-- Loading spinner -->
                <div class="mvp-loading">
                    <div class="mvp-spinner"></div>
                </div>
                
                <!-- Play button overlay -->
                <div class="mvp-play-overlay">
                    <button class="mvp-play-btn-large" aria-label="Play video">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M8 5v14l11-7z"/>
                        </svg>
                    </button>
                </div>
                
                <!-- Controls overlay -->
                <div class="mvp-controls-overlay">
                    <!-- Top controls -->
                    <div class="mvp-top-controls">
                        <div class="mvp-title"></div>
                        <button class="mvp-pip-btn" aria-label="Picture in Picture">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19 7h-8v6h8V7zm2-4H3c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H3V5h18v14z"/>
                            </svg>
                        </button>
                    </div>
                    
                    <!-- Bottom controls -->
                    <div class="mvp-bottom-controls">
                        <!-- Progress bar -->
                        <div class="mvp-progress-container">
                            <div class="mvp-progress-bar">
                                <div class="mvp-progress-buffer"></div>
                                <div class="mvp-progress-played"></div>
                                <div class="mvp-progress-handle"></div>
                            </div>
                            <div class="mvp-time-tooltip">
                                <div class="mvp-time-tooltip-content">00:00</div>
                            </div>
                        </div>
                        
                        <!-- Control buttons -->
                        <div class="mvp-controls-row">
                            <div class="mvp-controls-left">
                                <button class="mvp-play-btn" aria-label="Play/Pause">
                                    <svg class="mvp-play-icon" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M8 5v14l11-7z"/>
                                    </svg>
                                    <svg class="mvp-pause-icon" viewBox="0 0 24 24" fill="currentColor" style="display: none;">
                                        <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                                    </svg>
                                </button>
                                
                                <div class="mvp-volume-container">
                                    <button class="mvp-volume-btn" aria-label="Mute/Unmute">
                                        <svg class="mvp-volume-high" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
                                        </svg>
                                        <svg class="mvp-volume-muted" viewBox="0 0 24 24" fill="currentColor" style="display: none;">
                                            <path d="M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z"/>
                                        </svg>
                                    </button>
                                    <div class="mvp-volume-slider">
                                        <div class="mvp-volume-bar">
                                            <div class="mvp-volume-fill"></div>
                                            <div class="mvp-volume-handle"></div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mvp-time-display">
                                    <span class="mvp-current-time">00:00</span>
                                    <span class="mvp-time-separator">/</span>
                                    <span class="mvp-duration">00:00</span>
                                </div>
                            </div>
                            
                            <div class="mvp-controls-right">
                                <button class="mvp-settings-btn" aria-label="Settings">
                                    <svg viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
                                    </svg>
                                </button>
                                
                                <button class="mvp-fullscreen-btn" aria-label="Fullscreen">
                                    <svg class="mvp-fullscreen-enter" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"/>
                                    </svg>
                                    <svg class="mvp-fullscreen-exit" viewBox="0 0 24 24" fill="currentColor" style="display: none;">
                                        <path d="M5 16h3v3h2v-5H5v2zm3-8H5v2h5V5H8v3zm6 11h2v-3h3v-2h-5v5zm2-11V5h-2v5h5V8h-3z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Settings menu -->
            <div class="mvp-settings-menu" style="display: none;">
                <div class="mvp-settings-content">
                    <div class="mvp-settings-item">
                        <label>Playback Speed</label>
                        <select class="mvp-speed-select">
                            <option value="0.25">0.25x</option>
                            <option value="0.5">0.5x</option>
                            <option value="0.75">0.75x</option>
                            <option value="1" selected>Normal</option>
                            <option value="1.25">1.25x</option>
                            <option value="1.5">1.5x</option>
                            <option value="2">2x</option>
                        </select>
                    </div>
                    <div class="mvp-settings-item">
                        <label>Quality</label>
                        <select class="mvp-quality-select">
                            <option value="auto" selected>Auto</option>
                            <option value="1080p">1080p</option>
                            <option value="720p">720p</option>
                            <option value="480p">480p</option>
                            <option value="360p">360p</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    `
};

export default VideoPlayerTemplate;
