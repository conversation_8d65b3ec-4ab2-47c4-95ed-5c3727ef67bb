# Examples - Modern Video Player

## Basic Examples

### 1. Video MP4 cơ bản
```html
<div class="modern-video-player" 
     data-src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4"
     data-poster="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/BigBuckBunny.jpg">
</div>
```

### 2. YouTube Video
```html
<div class="modern-video-player" 
     data-youtube-id="dQw4w9WgXcQ">
</div>
```

### 3. Autoplay với Muted
```html
<div class="modern-video-player" 
     data-src="video.mp4"
     data-autoplay="true"
     data-muted="true">
</div>
```

### 4. Lazy Loading
```html
<div class="modern-video-player" 
     data-src="video.mp4"
     data-lazy="true">
</div>
```

## Advanced Examples

### 5. Programmatic Control
```html
<div id="my-video" class="modern-video-player"></div>
<div class="controls">
    <button onclick="playVideo()">Play</button>
    <button onclick="pauseVideo()">Pause</button>
    <button onclick="seekTo(30)">Seek to 30s</button>
    <button onclick="setVolume(0.5)">Volume 50%</button>
</div>

<script>
const player = new ModernVideoPlayer(document.getElementById('my-video'), {
    src: 'video.mp4',
    autoplay: false
});

function playVideo() {
    player.play();
}

function pauseVideo() {
    player.pause();
}

function seekTo(time) {
    player.seek(time);
}

function setVolume(volume) {
    player.setVolume(volume);
}
</script>
```

### 6. Event Handling
```html
<div id="video-with-events" class="modern-video-player" 
     data-src="video.mp4"></div>

<div id="event-log"></div>

<script>
const container = document.getElementById('video-with-events');
const log = document.getElementById('event-log');

// Listen to events
container.addEventListener('mvp:play', () => {
    log.innerHTML += '<p>Video started playing</p>';
});

container.addEventListener('mvp:pause', () => {
    log.innerHTML += '<p>Video paused</p>';
});

container.addEventListener('mvp:timeupdate', (e) => {
    const time = Math.floor(e.detail.currentTime);
    log.innerHTML += `<p>Time: ${time}s</p>`;
});

container.addEventListener('mvp:ended', () => {
    log.innerHTML += '<p>Video ended</p>';
});
</script>
```

### 7. Multiple Videos với Playlist
```html
<div class="playlist-container">
    <div class="video-list">
        <button onclick="loadVideo(0)">Video 1</button>
        <button onclick="loadVideo(1)">Video 2</button>
        <button onclick="loadVideo(2)">Video 3</button>
    </div>
    
    <div id="playlist-player" class="modern-video-player"></div>
</div>

<script>
const videos = [
    { src: 'video1.mp4', title: 'Video 1' },
    { src: 'video2.mp4', title: 'Video 2' },
    { youtubeId: 'dQw4w9WgXcQ', title: 'YouTube Video' }
];

let currentPlayer = null;

function loadVideo(index) {
    const container = document.getElementById('playlist-player');
    
    // Destroy current player
    if (currentPlayer) {
        currentPlayer.destroy();
    }
    
    // Create new player
    currentPlayer = new ModernVideoPlayer(container, videos[index]);
}

// Load first video
loadVideo(0);
</script>
```

### 8. Custom Styling
```html
<style>
.custom-video-player {
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
}

.custom-video-player .mvp-controls-overlay {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
}

.custom-video-player .mvp-progress-played {
    background: #fff;
}

.custom-video-player .mvp-play-btn-large {
    background: rgba(255,255,255,0.9);
    color: #333;
}
</style>

<div class="modern-video-player custom-video-player" 
     data-src="video.mp4">
</div>
```

### 9. Responsive Grid Layout
```html
<style>
.video-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    padding: 20px;
}

.video-grid .modern-video-player {
    aspect-ratio: 16/9;
}
</style>

<div class="video-grid">
    <div class="modern-video-player" data-src="video1.mp4"></div>
    <div class="modern-video-player" data-src="video2.mp4"></div>
    <div class="modern-video-player" data-src="video3.mp4"></div>
    <div class="modern-video-player" data-src="video4.mp4"></div>
</div>
```

### 10. Video với Subtitles/Captions
```html
<div class="modern-video-player" data-src="video.mp4">
    <!-- Subtitles sẽ được thêm vào video element -->
</div>

<script>
const player = new ModernVideoPlayer(container, {
    src: 'video.mp4'
});

// Add subtitles after player is initialized
player.video.innerHTML = `
    <track kind="subtitles" src="subtitles-en.vtt" srclang="en" label="English" default>
    <track kind="subtitles" src="subtitles-vi.vtt" srclang="vi" label="Tiếng Việt">
`;
</script>
```

### 11. Picture-in-Picture Integration
```html
<div class="modern-video-player" data-src="video.mp4"></div>
<button onclick="togglePiP()">Toggle Picture-in-Picture</button>

<script>
let player;

document.addEventListener('DOMContentLoaded', () => {
    player = new ModernVideoPlayer(document.querySelector('.modern-video-player'), {
        src: 'video.mp4'
    });
});

function togglePiP() {
    if (player) {
        player.togglePictureInPicture();
    }
}
</script>
```

### 12. Video Analytics
```html
<div class="modern-video-player" data-src="video.mp4"></div>

<script>
const analytics = {
    playCount: 0,
    totalWatchTime: 0,
    lastPlayTime: 0
};

container.addEventListener('mvp:play', () => {
    analytics.playCount++;
    analytics.lastPlayTime = Date.now();
    console.log('Play count:', analytics.playCount);
});

container.addEventListener('mvp:pause', () => {
    if (analytics.lastPlayTime) {
        analytics.totalWatchTime += Date.now() - analytics.lastPlayTime;
        console.log('Total watch time:', analytics.totalWatchTime / 1000, 'seconds');
    }
});

container.addEventListener('mvp:ended', () => {
    console.log('Video completed!');
    // Send analytics to server
    sendAnalytics(analytics);
});

function sendAnalytics(data) {
    // Send to your analytics service
    fetch('/api/video-analytics', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
    });
}
</script>
```

### 13. Mobile-Optimized Video
```html
<style>
@media (max-width: 768px) {
    .mobile-video {
        width: 100vw;
        margin-left: calc(-50vw + 50%);
    }
}
</style>

<div class="modern-video-player mobile-video" 
     data-src="video.mp4"
     data-autoplay="false">
</div>
```

### 14. Video với Loading States
```html
<div class="video-container">
    <div class="modern-video-player" data-src="large-video.mp4"></div>
    <div class="loading-overlay">
        <div class="spinner"></div>
        <p>Loading video...</p>
    </div>
</div>

<style>
.video-container {
    position: relative;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255,255,255,0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>

<script>
const container = document.querySelector('.modern-video-player');
const overlay = document.querySelector('.loading-overlay');

container.addEventListener('mvp:canplay', () => {
    overlay.style.display = 'none';
});
</script>
```

## Integration Examples

### React Component
```jsx
import React, { useEffect, useRef } from 'react';

const VideoPlayer = ({ src, youtubeId, autoplay = false }) => {
    const containerRef = useRef(null);
    const playerRef = useRef(null);
    
    useEffect(() => {
        if (containerRef.current) {
            playerRef.current = new ModernVideoPlayer(containerRef.current, {
                src,
                youtubeId,
                autoplay
            });
        }
        
        return () => {
            if (playerRef.current) {
                playerRef.current.destroy();
            }
        };
    }, [src, youtubeId, autoplay]);
    
    return <div ref={containerRef} className="modern-video-player" />;
};

export default VideoPlayer;
```

### Vue Component
```vue
<template>
    <div ref="container" class="modern-video-player"></div>
</template>

<script>
export default {
    props: {
        src: String,
        youtubeId: String,
        autoplay: Boolean
    },
    
    mounted() {
        this.player = new ModernVideoPlayer(this.$refs.container, {
            src: this.src,
            youtubeId: this.youtubeId,
            autoplay: this.autoplay
        });
    },
    
    beforeDestroy() {
        if (this.player) {
            this.player.destroy();
        }
    }
};
</script>
```
