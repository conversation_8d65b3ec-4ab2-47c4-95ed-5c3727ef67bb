# Hướng dẫn Cài đặt - Modern Video Player

## Y<PERSON>u cầu hệ thống

### Tr<PERSON><PERSON> duyệt hỗ trợ
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### Dependencies
- Không yêu cầu dependencies bên ngoài
- <PERSON><PERSON><PERSON> chọn: YouTube IFrame API (tự động load khi cần)

## Cài đặt

### Cách 1: Download trực tiếp

1. Download các file từ repository:
```
src/
├── css/
│   └── video-player.css
├── js/
│   ├── video-player.js
│   ├── browser-compatibility.js
│   └── performance-optimizations.js
```

2. Include vào HTML:
```html
<!DOCTYPE html>
<html>
<head>
    <link rel="stylesheet" href="src/css/video-player.css">
</head>
<body>
    <!-- Video player containers -->
    
    <script src="src/js/browser-compatibility.js"></script>
    <script src="src/js/performance-optimizations.js"></script>
    <script src="src/js/video-player.js"></script>
</body>
</html>
```

### Cách 2: Sử dụng npm (nếu có)

```bash
npm install modern-video-player
```

```javascript
import ModernVideoPlayer from 'modern-video-player';
import 'modern-video-player/dist/css/video-player.css';
```

### Cách 3: CDN (nếu có)

```html
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/modern-video-player/dist/css/video-player.min.css">
<script src="https://cdn.jsdelivr.net/npm/modern-video-player/dist/js/video-player.min.js"></script>
```

## Cấu hình cơ bản

### HTML Structure
```html
<div class="modern-video-player" 
     data-src="path/to/video.mp4"
     data-autoplay="false">
</div>
```

### JavaScript Initialization
```javascript
// Tự động khởi tạo tất cả video players
document.addEventListener('DOMContentLoaded', function() {
    ModernVideoPlayer.initAll();
});
```

## Cấu hình nâng cao

### Custom Options
```javascript
const player = new ModernVideoPlayer(container, {
    src: 'video.mp4',
    autoplay: false,
    muted: false,
    controls: true,
    preload: 'metadata',
    poster: 'poster.jpg'
});
```

### YouTube Integration
```html
<div class="modern-video-player" 
     data-youtube-id="dQw4w9WgXcQ">
</div>
```

### Performance Optimization
```html
<!-- Lazy loading -->
<div class="modern-video-player" 
     data-src="video.mp4"
     data-lazy="true">
</div>
```

## Build từ source

### Prerequisites
```bash
npm install
```

### Development
```bash
npm run dev
```

### Build production
```bash
npm run build
```

### File structure sau build:
```
dist/
├── css/
│   └── video-player.min.css
└── js/
    └── video-player.min.js
```

## Troubleshooting

### Video không phát được
1. Kiểm tra format video có được hỗ trợ
2. Kiểm tra CORS headers cho video từ domain khác
3. Kiểm tra autoplay policies của trình duyệt

### YouTube video không load
1. Kiểm tra YouTube video ID
2. Kiểm tra kết nối internet
3. Kiểm tra YouTube API có bị block không

### Controls không hiển thị trên mobile
1. Kiểm tra viewport meta tag
2. Kiểm tra CSS responsive
3. Kiểm tra touch events

### Performance issues
1. Bật lazy loading cho nhiều video
2. Sử dụng poster images
3. Kiểm tra network conditions

## Browser-specific notes

### Safari
- Yêu cầu user interaction cho autoplay
- Hỗ trợ Picture-in-Picture qua webkitSetPresentationMode

### Chrome
- Autoplay policy nghiêm ngặt
- Hỗ trợ đầy đủ các tính năng hiện đại

### Firefox
- Autoplay policy linh hoạt hơn
- Một số CSS properties cần prefix

### Edge
- Tương tự Chrome (Chromium-based)
- Hỗ trợ tốt các tính năng mới

## Security considerations

### Content Security Policy
```html
<meta http-equiv="Content-Security-Policy" 
      content="media-src 'self' https://www.youtube.com https://commondatastorage.googleapis.com;">
```

### HTTPS requirement
- YouTube API yêu cầu HTTPS
- Autoplay policies yêu cầu HTTPS
- Picture-in-Picture yêu cầu HTTPS

## Examples

Xem thêm examples trong thư mục `demo/`:
- Basic video playback
- YouTube integration
- Autoplay with muted
- Lazy loading
- Custom styling
