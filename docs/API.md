# Modern Video Player - API Documentation

## Khởi tạo

### Cách 1: Tự động khởi tạo
```html
<div class="modern-video-player" 
     data-src="path/to/video.mp4"
     data-autoplay="false"
     data-muted="false">
</div>

<script src="src/js/video-player.js"></script>
```

### Cách 2: Khởi tạo thủ công
```javascript
const player = new ModernVideoPlayer(container, {
    src: 'path/to/video.mp4',
    autoplay: false,
    muted: false,
    controls: true,
    poster: 'path/to/poster.jpg'
});
```

### Cách 3: Khởi tạo với YouTube
```javascript
const player = new ModernVideoPlayer(container, {
    youtubeId: 'dQw4w9WgXcQ',
    autoplay: false
});
```

## Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `src` | string | null | URL của video file |
| `youtubeId` | string | null | YouTube video ID |
| `autoplay` | boolean | false | Tự động phát video |
| `muted` | boolean | false | Tắt tiếng khi khởi tạo |
| `controls` | boolean | true | Hiển thị controls |
| `preload` | string | 'metadata' | Preload strategy |
| `playsinline` | boolean | true | Phát inline trên mobile |
| `poster` | string | null | URL của poster image |

## Methods

### Playback Control
```javascript
player.play()           // Phát video
player.pause()          // Tạm dừng video
player.togglePlay()     // Toggle play/pause
player.seek(time)       // Tua đến thời điểm (giây)
```

### Volume Control
```javascript
player.setVolume(0.5)   // Set volume (0-1)
player.toggleMute()     // Toggle mute/unmute
```

### Fullscreen
```javascript
player.toggleFullscreen()     // Toggle fullscreen
player.enterFullscreen()      // Vào fullscreen
player.exitFullscreen()       // Thoát fullscreen
```

### Picture-in-Picture
```javascript
player.togglePictureInPicture()  // Toggle PiP mode
```

### Settings
```javascript
player.changePlaybackSpeed(1.5)  // Thay đổi tốc độ phát (0.25-2x)
```

### Utility
```javascript
player.destroy()        // Cleanup và destroy player
```

## Events

Video player phát ra các events tùy chỉnh:

```javascript
// Lắng nghe events
container.addEventListener('mvp:play', () => {
    console.log('Video started playing');
});

container.addEventListener('mvp:pause', () => {
    console.log('Video paused');
});

container.addEventListener('mvp:timeupdate', (e) => {
    console.log('Current time:', e.detail.currentTime);
});

container.addEventListener('mvp:ended', () => {
    console.log('Video ended');
});
```

### Danh sách Events
- `mvp:play` - Video bắt đầu phát
- `mvp:pause` - Video tạm dừng
- `mvp:ended` - Video kết thúc
- `mvp:timeupdate` - Cập nhật thời gian
- `mvp:volumechange` - Thay đổi volume
- `mvp:fullscreenchange` - Thay đổi fullscreen
- `mvp:error` - Lỗi xảy ra

## Static Methods

```javascript
// Khởi tạo tất cả video players trên trang
ModernVideoPlayer.initAll();

// Khởi tạo player cho selector cụ thể
ModernVideoPlayer.init('#my-video', options);

// Sử dụng lazy loading (tự động)
ModernVideoPlayer.observeVideos();
```

## Data Attributes

Có thể sử dụng data attributes để cấu hình:

```html
<div class="modern-video-player"
     data-src="video.mp4"
     data-youtube-id="dQw4w9WgXcQ"
     data-autoplay="true"
     data-muted="true"
     data-poster="poster.jpg"
     data-lazy="true">
</div>
```

## Browser Compatibility

### Supported Browsers
- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+

### Autoplay Policies
Thư viện tự động tuân thủ autoplay policies của các trình duyệt:

- **Chrome/Edge**: Yêu cầu user interaction hoặc video muted
- **Firefox**: Cho phép autoplay với một số hạn chế
- **Safari**: Yêu cầu user interaction hoặc video muted

### Feature Detection
```javascript
// Kiểm tra browser compatibility
const compatibility = new BrowserCompatibility();

// Kiểm tra autoplay support
if (compatibility.canAutoplay(video, { muted: true })) {
    // Có thể autoplay
}

// Kiểm tra PiP support
if (compatibility.pipSupported) {
    // Picture-in-Picture được hỗ trợ
}
```

## Performance Features

### Lazy Loading
```html
<!-- Video sẽ chỉ load khi gần viewport -->
<div class="modern-video-player" 
     data-src="video.mp4"
     data-lazy="true">
</div>
```

### GPU Acceleration
Tự động được bật cho:
- Video elements
- Progress bars
- Volume controls
- Animations

### Prefetching
- Tự động prefetch videos gần viewport
- Prefetch on hover
- Smart network-aware loading

## Keyboard Shortcuts

| Key | Action |
|-----|--------|
| Space | Play/Pause |
| ← | Seek backward 10s |
| → | Seek forward 10s |
| ↑ | Volume up |
| ↓ | Volume down |
| M | Mute/Unmute |
| F | Fullscreen toggle |

## Mobile Support

### Touch Events
- Tap to play/pause
- Swipe for seeking
- Pinch for volume (planned)

### Responsive Design
- Adaptive controls size
- Touch-friendly buttons
- Optimized for portrait/landscape

## Error Handling

```javascript
container.addEventListener('mvp:error', (e) => {
    console.error('Video error:', e.detail.error);
    
    // Handle different error types
    switch (e.detail.error.code) {
        case 1: // MEDIA_ERR_ABORTED
            break;
        case 2: // MEDIA_ERR_NETWORK
            break;
        case 3: // MEDIA_ERR_DECODE
            break;
        case 4: // MEDIA_ERR_SRC_NOT_SUPPORTED
            break;
    }
});
```
