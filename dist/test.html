<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Video Player - Test Page</title>
    <link rel="stylesheet" href="./css/video-player.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .test-controls {
            margin: 20px 0;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .test-controls button {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            background: #007bff;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
        .test-controls button:hover {
            background: #0056b3;
        }
        .test-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .video-wrapper {
            margin: 20px 0;
            max-width: 800px;
        }
        h1, h2 {
            color: #333;
        }
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Modern Video Player - Test Suite</h1>
        
        <!-- Test 1: Basic Functionality -->
        <div class="test-section">
            <h2>Test 1: Basic Video Playback</h2>
            <div class="video-wrapper">
                <div id="test-video-1" class="modern-video-player" 
                     data-src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4"
                     data-poster="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/BigBuckBunny.jpg">
                </div>
            </div>
            <div class="test-controls">
                <button onclick="testPlay(1)">Test Play</button>
                <button onclick="testPause(1)">Test Pause</button>
                <button onclick="testSeek(1, 30)">Seek to 30s</button>
                <button onclick="testVolume(1, 0.5)">Volume 50%</button>
                <button onclick="testMute(1)">Toggle Mute</button>
                <button onclick="testFullscreen(1)">Toggle Fullscreen</button>
            </div>
            <div id="test-log-1" class="test-log"></div>
        </div>

        <!-- Test 2: YouTube Integration -->
        <div class="test-section">
            <h2>Test 2: YouTube Video</h2>
            <div class="video-wrapper">
                <div id="test-video-2" class="modern-video-player" 
                     data-youtube-id="dQw4w9WgXcQ">
                </div>
            </div>
            <div class="test-controls">
                <button onclick="testPlay(2)">Test Play</button>
                <button onclick="testPause(2)">Test Pause</button>
                <button onclick="testSeek(2, 60)">Seek to 1min</button>
                <button onclick="testSpeed(2, 1.5)">Speed 1.5x</button>
            </div>
            <div id="test-log-2" class="test-log"></div>
        </div>

        <!-- Test 3: Programmatic Control -->
        <div class="test-section">
            <h2>Test 3: Programmatic Control</h2>
            <div class="video-wrapper">
                <div id="test-video-3" class="modern-video-player"></div>
            </div>
            <div class="test-controls">
                <button onclick="loadVideo(3, 'mp4')">Load MP4</button>
                <button onclick="loadVideo(3, 'youtube')">Load YouTube</button>
                <button onclick="testDestroy(3)">Destroy Player</button>
                <button onclick="testRecreate(3)">Recreate Player</button>
            </div>
            <div id="test-log-3" class="test-log"></div>
        </div>

        <!-- Test 4: Event Handling -->
        <div class="test-section">
            <h2>Test 4: Event Handling</h2>
            <div class="video-wrapper">
                <div id="test-video-4" class="modern-video-player" 
                     data-src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4">
                </div>
            </div>
            <div class="test-controls">
                <button onclick="startEventTest(4)">Start Event Test</button>
                <button onclick="clearEventLog(4)">Clear Log</button>
            </div>
            <div id="test-log-4" class="test-log"></div>
        </div>

        <!-- Test 5: Performance Test -->
        <div class="test-section">
            <h2>Test 5: Performance & Multiple Videos</h2>
            <div class="test-controls">
                <button onclick="createMultipleVideos()">Create 5 Videos</button>
                <button onclick="testLazyLoading()">Test Lazy Loading</button>
                <button onclick="measurePerformance()">Measure Performance</button>
                <button onclick="cleanupMultipleVideos()">Cleanup</button>
            </div>
            <div id="multiple-videos-container"></div>
            <div id="test-log-5" class="test-log"></div>
        </div>

        <!-- Test Results Summary -->
        <div class="test-section">
            <h2>📊 Test Results Summary</h2>
            <div id="test-results">
                <div class="status info">
                    <strong>Test Status:</strong> Ready to run tests
                </div>
            </div>
        </div>
    </div>

    <!-- Include all JavaScript files -->
    <!-- <script src="../src/js/browser-compatibility.js"></script> -->
    <!-- <script src="../src/js/performance-optimizations.js"></script> -->
    <!-- <script src="../src/js/video-player.js"></script> -->

    <script src="./js/browser-compatibility.min.js"></script>
    <script src="./js/performance-optimizations.min.js"></script>
    <script src="./js/video-player.min.js"></script>

    <script>
        // Test variables
        let testPlayers = {};
        let testResults = {
            passed: 0,
            failed: 0,
            total: 0
        };

        // Initialize test players
        document.addEventListener('DOMContentLoaded', function() {
            initializeTestPlayers();
        });

        function initializeTestPlayers() {
            // Initialize basic players
            for (let i = 1; i <= 4; i++) {
                const container = document.getElementById(`test-video-${i}`);
                if (container && !container.dataset.initialized) {
                    try {
                        if (i === 2) {
                            // YouTube player
                            testPlayers[i] = new ModernVideoPlayer(container, {
                                youtubeId: 'dQw4w9WgXcQ'
                            });
                        } else if (i === 3) {
                            // Programmatic player (empty initially)
                            testPlayers[i] = null;
                        } else {
                            // Regular video players
                            const src = container.dataset.src;
                            const poster = container.dataset.poster;
                            testPlayers[i] = new ModernVideoPlayer(container, {
                                src: src,
                                poster: poster
                            });
                        }
                        container.dataset.initialized = 'true';
                        logTest(i, `Player ${i} initialized successfully`, 'success');
                    } catch (error) {
                        logTest(i, `Failed to initialize player ${i}: ${error.message}`, 'error');
                    }
                }
            }
        }

        // Test functions
        function testPlay(playerId) {
            try {
                if (testPlayers[playerId]) {
                    testPlayers[playerId].play();
                    logTest(playerId, 'Play command executed', 'success');
                } else {
                    logTest(playerId, 'Player not found', 'error');
                }
            } catch (error) {
                logTest(playerId, `Play failed: ${error.message}`, 'error');
            }
        }

        function testPause(playerId) {
            try {
                if (testPlayers[playerId]) {
                    testPlayers[playerId].pause();
                    logTest(playerId, 'Pause command executed', 'success');
                } else {
                    logTest(playerId, 'Player not found', 'error');
                }
            } catch (error) {
                logTest(playerId, `Pause failed: ${error.message}`, 'error');
            }
        }

        function testSeek(playerId, time) {
            try {
                if (testPlayers[playerId]) {
                    testPlayers[playerId].seek(time);
                    logTest(playerId, `Seek to ${time}s executed`, 'success');
                } else {
                    logTest(playerId, 'Player not found', 'error');
                }
            } catch (error) {
                logTest(playerId, `Seek failed: ${error.message}`, 'error');
            }
        }

        function testVolume(playerId, volume) {
            try {
                if (testPlayers[playerId]) {
                    testPlayers[playerId].setVolume(volume);
                    logTest(playerId, `Volume set to ${volume * 100}%`, 'success');
                } else {
                    logTest(playerId, 'Player not found', 'error');
                }
            } catch (error) {
                logTest(playerId, `Volume change failed: ${error.message}`, 'error');
            }
        }

        function testMute(playerId) {
            try {
                if (testPlayers[playerId]) {
                    testPlayers[playerId].toggleMute();
                    logTest(playerId, 'Mute toggle executed', 'success');
                } else {
                    logTest(playerId, 'Player not found', 'error');
                }
            } catch (error) {
                logTest(playerId, `Mute toggle failed: ${error.message}`, 'error');
            }
        }

        function testFullscreen(playerId) {
            try {
                if (testPlayers[playerId]) {
                    testPlayers[playerId].toggleFullscreen();
                    logTest(playerId, 'Fullscreen toggle executed', 'success');
                } else {
                    logTest(playerId, 'Player not found', 'error');
                }
            } catch (error) {
                logTest(playerId, `Fullscreen toggle failed: ${error.message}`, 'error');
            }
        }

        function testSpeed(playerId, speed) {
            try {
                if (testPlayers[playerId]) {
                    testPlayers[playerId].changePlaybackSpeed(speed);
                    logTest(playerId, `Playback speed set to ${speed}x`, 'success');
                } else {
                    logTest(playerId, 'Player not found', 'error');
                }
            } catch (error) {
                logTest(playerId, `Speed change failed: ${error.message}`, 'error');
            }
        }

        function loadVideo(playerId, type) {
            try {
                const container = document.getElementById(`test-video-${playerId}`);
                
                // Destroy existing player
                if (testPlayers[playerId]) {
                    testPlayers[playerId].destroy();
                }

                // Create new player
                if (type === 'mp4') {
                    testPlayers[playerId] = new ModernVideoPlayer(container, {
                        src: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4'
                    });
                    logTest(playerId, 'MP4 video loaded', 'success');
                } else if (type === 'youtube') {
                    testPlayers[playerId] = new ModernVideoPlayer(container, {
                        youtubeId: 'dQw4w9WgXcQ'
                    });
                    logTest(playerId, 'YouTube video loaded', 'success');
                }
            } catch (error) {
                logTest(playerId, `Load video failed: ${error.message}`, 'error');
            }
        }

        function testDestroy(playerId) {
            try {
                if (testPlayers[playerId]) {
                    testPlayers[playerId].destroy();
                    testPlayers[playerId] = null;
                    logTest(playerId, 'Player destroyed successfully', 'success');
                } else {
                    logTest(playerId, 'No player to destroy', 'info');
                }
            } catch (error) {
                logTest(playerId, `Destroy failed: ${error.message}`, 'error');
            }
        }

        function testRecreate(playerId) {
            try {
                const container = document.getElementById(`test-video-${playerId}`);
                testPlayers[playerId] = new ModernVideoPlayer(container, {
                    src: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4'
                });
                logTest(playerId, 'Player recreated successfully', 'success');
            } catch (error) {
                logTest(playerId, `Recreate failed: ${error.message}`, 'error');
            }
        }

        function startEventTest(playerId) {
            const container = document.getElementById(`test-video-${playerId}`);
            const events = ['mvp:play', 'mvp:pause', 'mvp:timeupdate', 'mvp:ended', 'mvp:volumechange'];
            
            events.forEach(eventName => {
                container.addEventListener(eventName, (e) => {
                    logTest(playerId, `Event: ${eventName} - ${JSON.stringify(e.detail || {})}`, 'info');
                });
            });
            
            logTest(playerId, 'Event listeners attached', 'success');
        }

        function clearEventLog(playerId) {
            document.getElementById(`test-log-${playerId}`).innerHTML = '';
        }

        function createMultipleVideos() {
            const container = document.getElementById('multiple-videos-container');
            container.innerHTML = '';
            
            for (let i = 0; i < 5; i++) {
                const videoDiv = document.createElement('div');
                videoDiv.className = 'modern-video-player';
                videoDiv.style.marginBottom = '20px';
                videoDiv.dataset.src = 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4';
                videoDiv.dataset.lazy = 'true';
                container.appendChild(videoDiv);
            }
            
            logTest(5, '5 videos created with lazy loading', 'success');
        }

        function testLazyLoading() {
            logTest(5, 'Testing lazy loading - scroll to see videos load', 'info');
            ModernVideoPlayer.observeVideos();
        }

        function measurePerformance() {
            const start = performance.now();
            
            // Create and destroy multiple players
            for (let i = 0; i < 10; i++) {
                const div = document.createElement('div');
                div.className = 'modern-video-player';
                document.body.appendChild(div);
                
                const player = new ModernVideoPlayer(div, {
                    src: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4'
                });
                
                player.destroy();
                document.body.removeChild(div);
            }
            
            const end = performance.now();
            logTest(5, `Performance test: ${(end - start).toFixed(2)}ms for 10 create/destroy cycles`, 'info');
        }

        function cleanupMultipleVideos() {
            document.getElementById('multiple-videos-container').innerHTML = '';
            logTest(5, 'Multiple videos cleaned up', 'success');
        }

        function logTest(playerId, message, type = 'info') {
            const log = document.getElementById(`test-log-${playerId}`);
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logEntry.style.color = type === 'success' ? 'green' : type === 'error' ? 'red' : 'blue';
            log.appendChild(logEntry);
            log.scrollTop = log.scrollHeight;
            
            // Update test results
            if (type === 'success') {
                testResults.passed++;
            } else if (type === 'error') {
                testResults.failed++;
            }
            testResults.total++;
            
            updateTestSummary();
        }

        function updateTestSummary() {
            const resultsDiv = document.getElementById('test-results');
            const passRate = testResults.total > 0 ? (testResults.passed / testResults.total * 100).toFixed(1) : 0;
            
            resultsDiv.innerHTML = `
                <div class="status ${testResults.failed === 0 ? 'success' : 'info'}">
                    <strong>Tests Run:</strong> ${testResults.total} | 
                    <strong>Passed:</strong> ${testResults.passed} | 
                    <strong>Failed:</strong> ${testResults.failed} | 
                    <strong>Pass Rate:</strong> ${passRate}%
                </div>
            `;
        }
    </script>
</body>
</html>
