:root{--mvp-primary:#007bff;--mvp-primary-hover:#0056b3;--mvp-bg-overlay:rgba(0,0,0,.7);--mvp-bg-controls:rgba(0,0,0,.8);--mvp-text-primary:#fff;--mvp-text-secondary:#ccc;--mvp-border-radius:8px;--mvp-transition:all 0.3s cubic-bezier(0.4,0,0.2,1);--mvp-shadow:0 4px 20px rgba(0,0,0,.3)}.modern-video-player,.mvp-container{aspect-ratio:16/9;background:#000;border-radius:var(--mvp-border-radius);box-shadow:var(--mvp-shadow);font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,sans-serif;max-width:100%;overflow:hidden;position:relative;-webkit-user-select:none;-moz-user-select:none;user-select:none;width:100%}.mvp-video-wrapper{position:relative}.mvp-video,.mvp-video-wrapper{background:#000;cursor:pointer;height:100%;width:100%}.mvp-video{-o-object-fit:contain;object-fit:contain}.mvp-youtube-wrapper{left:0;position:absolute;top:0}.mvp-youtube-player,.mvp-youtube-wrapper{height:100%;width:100%}.mvp-loading{left:50%;position:absolute;top:50%;transform:translate(-50%,-50%);z-index:10}.mvp-spinner{animation:mvp-spin 1s linear infinite;border:3px solid hsla(0,0%,100%,.3);border-radius:50%;border-top:3px solid var(--mvp-primary);height:40px;width:40px}@keyframes mvp-spin{0%{transform:rotate(0deg)}to{transform:rotate(1turn)}}.mvp-play-overlay{align-items:center;background:var(--mvp-bg-overlay);display:flex;height:100%;justify-content:center;left:0;opacity:1;position:absolute;top:0;transition:var(--mvp-transition);width:100%;z-index:5}.mvp-play-overlay.hidden{opacity:0;pointer-events:none}.mvp-play-btn-large{align-items:center;background:var(--mvp-primary);border:none;border-radius:50%;box-shadow:0 4px 20px rgba(0,123,255,.4);color:#fff;cursor:pointer;display:flex;height:80px;justify-content:center;transition:var(--mvp-transition);width:80px}.mvp-play-btn-large:hover{background:var(--mvp-primary-hover);transform:scale(1.1)}.mvp-play-btn-large svg{height:32px;margin-left:4px;width:32px}.mvp-controls-overlay{display:flex;flex-direction:column;height:100%;justify-content:space-between;left:0;opacity:0;pointer-events:none;position:absolute;top:0;transition:var(--mvp-transition);width:100%;z-index:15}.mvp-container:hover .mvp-controls-overlay,.mvp-controls-overlay.visible{opacity:1}.mvp-container:hover .mvp-controls-overlay *,.mvp-controls-overlay.visible *{pointer-events:auto}.mvp-top-controls{align-items:center;background:linear-gradient(180deg,var(--mvp-bg-controls) 0,transparent 100%);display:flex;justify-content:space-between;padding:20px}.mvp-title{color:var(--mvp-text-primary);font-size:16px;font-weight:500}.mvp-bottom-controls{background:linear-gradient(0deg,var(--mvp-bg-controls) 0,transparent 100%);padding:0 20px 20px}.mvp-progress-container{align-items:center;display:flex;height:20px;margin-bottom:15px;position:relative}.mvp-progress-bar{background:hsla(0,0%,100%,.3);border-radius:2px;cursor:pointer;height:4px;position:relative;transition:height .2s ease;width:100%}.mvp-progress-container:hover .mvp-progress-bar{height:6px}.mvp-progress-buffer{background:hsla(0,0%,100%,.5);transition:width .3s ease}.mvp-progress-buffer,.mvp-progress-played{border-radius:2px;height:100%;left:0;position:absolute;top:0;width:0}.mvp-progress-played{background:var(--mvp-primary);transition:width .1s ease}.mvp-progress-handle{background:var(--mvp-primary);border-radius:50%;height:12px;left:0;opacity:0;position:absolute;top:50%;transform:translate(-50%,-50%);transition:var(--mvp-transition);width:12px}.mvp-progress-container:hover .mvp-progress-handle{opacity:1}.mvp-time-tooltip{background:var(--mvp-bg-controls);border-radius:4px;bottom:25px;color:var(--mvp-text-primary);font-size:12px;opacity:0;padding:4px 8px;pointer-events:none;position:absolute;transform:translateX(-50%);transition:var(--mvp-transition);white-space:nowrap}.mvp-time-tooltip.visible{opacity:1}.mvp-controls-row{align-items:center;display:flex;justify-content:space-between}.mvp-controls-left,.mvp-controls-right{align-items:center;display:flex;gap:12px}.mvp-controls-overlay button{align-items:center;background:none;border:none;border-radius:4px;color:var(--mvp-text-primary);cursor:pointer;display:flex;justify-content:center;padding:8px;transition:var(--mvp-transition)}.mvp-controls-overlay button:hover{background:hsla(0,0%,100%,.1);color:var(--mvp-primary)}.mvp-controls-overlay button svg{height:20px;width:20px}.mvp-play-btn svg{height:24px;width:24px}.mvp-volume-container{align-items:center;display:flex;gap:8px}.mvp-volume-slider{overflow:hidden;transition:width .3s ease;width:0}.mvp-volume-container:hover .mvp-volume-slider{width:80px}.mvp-volume-bar{background:hsla(0,0%,100%,.3);border-radius:2px;cursor:pointer;height:4px;position:relative;width:80px}.mvp-volume-fill{border-radius:2px;height:100%;left:0;top:0;transition:width .1s ease;width:100%}.mvp-volume-fill,.mvp-volume-handle{background:var(--mvp-primary);position:absolute}.mvp-volume-handle{border-radius:50%;cursor:pointer;height:12px;right:0;top:50%;transform:translate(50%,-50%);width:12px}.mvp-time-display{color:var(--mvp-text-secondary);font-size:14px;font-weight:500;white-space:nowrap}.mvp-time-separator{margin:0 4px}.mvp-settings-menu{-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);background:var(--mvp-bg-controls);border-radius:var(--mvp-border-radius);bottom:70px;min-width:200px;padding:15px;position:absolute;right:20px;z-index:20}.mvp-settings-item{align-items:center;color:var(--mvp-text-primary);display:flex;font-size:14px;justify-content:space-between;margin-bottom:10px}.mvp-settings-item:last-child{margin-bottom:0}.mvp-settings-item select{background:hsla(0,0%,100%,.1);border:1px solid hsla(0,0%,100%,.2);border-radius:4px;color:var(--mvp-text-primary);font-size:12px;padding:4px 8px}@media (max-width:768px){.mvp-bottom-controls,.mvp-top-controls{padding:15px}.mvp-controls-left,.mvp-controls-right{gap:8px}.mvp-controls-overlay button{padding:6px}.mvp-controls-overlay button svg{height:18px;width:18px}.mvp-play-btn svg{height:20px;width:20px}.mvp-play-btn-large{height:60px;width:60px}.mvp-play-btn-large svg{height:24px;width:24px}.mvp-time-display{font-size:12px}.mvp-volume-bar,.mvp-volume-container:hover .mvp-volume-slider{width:60px}.mvp-settings-menu{bottom:60px;min-width:180px;right:10px}}@media (max-width:480px){.mvp-bottom-controls,.mvp-top-controls{padding:10px}.mvp-progress-container{margin-bottom:10px}.mvp-controls-left,.mvp-controls-right{gap:6px}.mvp-volume-slider{display:none}.mvp-settings-menu{border-radius:var(--mvp-border-radius) var(--mvp-border-radius) 0 0;bottom:0;left:0;min-width:auto;position:fixed;right:0}}.mvp-container:fullscreen{border-radius:0;height:100vh;width:100vw}.modern-video-player[data-lazy=true]:not([data-initialized]){align-items:center;background:#f0f0f0;display:flex;justify-content:center;position:relative}.modern-video-player[data-lazy=true]:not([data-initialized]):before{color:#666;content:"📹 Video sẽ tải khi cuộn đến đây...";font-size:16px;padding:20px;text-align:center}.mvp-controls-overlay,.mvp-progress-played,.mvp-video,.mvp-volume-fill{transform:translateZ(0);will-change:transform}*{box-sizing:border-box}.mvp-container *{transition-timing-function:cubic-bezier(.4,0,.2,1)}
/*# sourceMappingURL=data:application/json;base64,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 */