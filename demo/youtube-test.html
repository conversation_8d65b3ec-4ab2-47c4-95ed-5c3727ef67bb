<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>YouTube Videos</title>
    <link rel="stylesheet" href="../src/css/video-player.css">
    <style>
        body { 
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif; 
            background: #f0f0f0;
        }
        .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin: 0 auto;
        }
        .video-item {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .modern-video-player {
            width: 100%;
            height: 250px;
        }
        @media (max-width: 768px) {
            .video-grid {
                grid-template-columns: 1fr;
            }
            .modern-video-player {
                height: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="video-grid">
        <!-- Video 1: Autoplay -->
        <div class="video-item">
            <div class="modern-video-player" 
                 data-youtube-id="dQw4w9WgXcQ"
                 data-autoplay="true"
                 data-muted="true">
            </div>
        </div>

        <!-- Video 2: Lazy Load -->
        <div class="video-item">
            <div class="modern-video-player" 
                 data-youtube-id="9bZkp7q19f0"
                 data-lazy="true">
            </div>
        </div>

        <!-- Video 3: Lazy Load -->
        <div class="video-item">
            <div class="modern-video-player" 
                 data-youtube-id="kJQP7kiw5Fk"
                 data-lazy="true">
            </div>
        </div>

        <!-- Video 4: Lazy Load -->
        <div class="video-item">
            <div class="modern-video-player" 
                 data-youtube-id="fJ9rUzIMcZQ"
                 data-lazy="true">
            </div>
        </div>

        <!-- Video 5: Lazy Load -->
        <div class="video-item">
            <div class="modern-video-player" 
                 data-youtube-id="L_jWHffIx5E"
                 data-lazy="true">
            </div>
        </div>

        <!-- Video 6: Lazy Load -->
        <div class="video-item">
            <div class="modern-video-player" 
                 data-youtube-id="ZZ5LpwO-An4"
                 data-lazy="true">
            </div>
        </div>
    </div>

    <script src="../src/js/video-player.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize first video immediately (with autoplay)
            const firstVideo = document.querySelector('.modern-video-player[data-autoplay="true"]');
            if (firstVideo) {
                new ModernVideoPlayer(firstVideo, {
                    youtubeId: firstVideo.dataset.youtubeId,
                    autoplay: true,
                    muted: true
                });
            }
            
            // Setup lazy loading for other videos
            ModernVideoPlayer.observeVideos();
        });
    </script>
</body>
</html>
