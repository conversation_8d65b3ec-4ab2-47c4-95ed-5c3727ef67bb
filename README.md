# Modern Video Player Library

Thư viện video player hiện đại với đầy đủ tính năng và tối ưu cho trải nghiệm người dùng.

## Tính năng

- ✅ Hỗ trợ video YouTube và các định dạng video khác
- ✅ Giao diện người dùng hiện đại và responsive
- ✅ Tối ưu UX cho cả mobile và desktop
- ✅ Tính năng scrubbing/seeking với preview thumbnails
- ✅ Cross-browser compatibility (Chrome, Firefox, Safari, Edge)
- ✅ Performance optimizations (lazy loading, GPU acceleration, prefetch)
- ✅ Autoplay settings có thể tùy chỉnh
- ✅ Keyboard shortcuts support
- ✅ Picture-in-Picture support
- ✅ Fullscreen support

## Cấu trúc thư mục

```
modern-video-player/
├── src/
│   ├── css/
│   │   └── video-player.css
│   ├── js/
│   │   └── video-player.js
│   └── assets/
│       └── icons/
├── dist/
│   ├── css/
│   └── js/
├── demo/
│   └── index.html
└── docs/
```

## Cài đặt

```bash
npm install
npm run dev
```

## Sử dụng

```html
<div id="video-container">
  <video-player 
    src="path/to/video.mp4"
    youtube-id="dQw4w9WgXcQ"
    autoplay="false"
    controls="true">
  </video-player>
</div>
```

## Quick Start

### 1. Include CSS và JavaScript
```html
<link rel="stylesheet" href="src/css/video-player.css">
<script src="src/js/browser-compatibility.js"></script>
<script src="src/js/performance-optimizations.js"></script>
<script src="src/js/video-player.js"></script>
```

### 2. Tạo video player
```html
<!-- Video MP4 -->
<div class="modern-video-player"
     data-src="path/to/video.mp4"
     data-autoplay="false">
</div>

<!-- YouTube Video -->
<div class="modern-video-player"
     data-youtube-id="dQw4w9WgXcQ">
</div>
```

### 3. Khởi tạo
```javascript
document.addEventListener('DOMContentLoaded', function() {
    ModernVideoPlayer.initAll();
});
```

## Demo và Test

- **Demo**: Mở `demo/index.html` để xem các ví dụ
- **Test Suite**: Mở `demo/test.html` để chạy test suite
- **Examples**: Xem `docs/EXAMPLES.md` cho các ví dụ chi tiết

## Browser Support

| Browser | Version | Status |
|---------|---------|--------|
| Chrome | 60+ | ✅ Full support |
| Firefox | 55+ | ✅ Full support |
| Safari | 12+ | ✅ Full support |
| Edge | 79+ | ✅ Full support |

## Performance Features

- 🚀 **Lazy Loading**: Videos chỉ load khi cần thiết
- ⚡ **GPU Acceleration**: Sử dụng hardware acceleration
- 📱 **Mobile Optimized**: Touch-friendly controls
- 🔄 **Adaptive Bitrate**: Tự động điều chỉnh chất lượng
- 💾 **Memory Management**: Tự động cleanup unused videos

## API Documentation

Xem chi tiết trong thư mục `docs/`:
- [`docs/API.md`](docs/API.md) - API Reference
- [`docs/INSTALLATION.md`](docs/INSTALLATION.md) - Hướng dẫn cài đặt
- [`docs/EXAMPLES.md`](docs/EXAMPLES.md) - Ví dụ sử dụng
