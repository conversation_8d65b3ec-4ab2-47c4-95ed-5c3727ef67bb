{"name": "modern-video-player", "version": "1.0.0", "description": "Modern video player library with YouTube support, responsive design, and performance optimizations", "main": "src/js/video-player.js", "scripts": {"dev": "live-server --port=3000", "build": "npm run build:css && npm run build:js", "build:css": "postcss src/css/video-player.css -o dist/css/video-player.min.css --use autoprefixer cssnano", "build:js": "terser src/js/performance-optimizations.js -o dist/js/performance-optimizations.min.js --compress --mangle", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["video", "player", "youtube", "responsive", "modern", "html5", "javascript"], "author": "Your Name", "license": "MIT", "devDependencies": {"autoprefixer": "^10.4.16", "cssnano": "^6.0.1", "live-server": "^1.2.2", "postcss": "^8.4.31", "postcss-cli": "^10.1.0", "terser": "^5.24.0"}, "dependencies": {}}